<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-Frame-Options" content="ALLOWALL">
    <title>🎮 2025 Game Dev Career Quiz</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            width: 100%;
            margin: 0;
            padding: 20px;
            background: #ffffff;
            box-sizing: border-box;
        }
        
        .quiz-container {
            width: 100%;
            max-width: none;
            padding: 30px;
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            box-sizing: border-box;
        }
        
        .quiz-title {
            font-size: 2.5rem;
            font-weight: bold;
            color: #1a1a1a;
            margin-bottom: 10px;
            text-align: center;
        }
        
        .quiz-subtitle {
            font-size: 1.1rem;
            color: #666;
            line-height: 1.5;
            text-align: center;
            margin-bottom: 30px;
        }
        
        /* CSS-only navigation system */
        .nav-radio {
            position: absolute;
            opacity: 0;
            pointer-events: none;
        }
        
        .question-slide {
            display: none;
            animation: fadeIn 0.3s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateX(20px); }
            to { opacity: 1; transform: translateX(0); }
        }
        
        /* Show questions based on navigation state */
        #nav-q1:checked ~ .question-slide.q1 { display: block; }
        #nav-q2:checked ~ .question-slide.q2 { display: block; }
        #nav-q3:checked ~ .question-slide.q3 { display: block; }
        #nav-q4:checked ~ .question-slide.q4 { display: block; }
        #nav-q5:checked ~ .question-slide.q5 { display: block; }
        #nav-results:checked ~ .question-slide.results { display: block; }
        
        /* Progress bar */
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #f0f0f0;
            border-radius: 4px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            border-radius: 4px;
            transition: width 0.3s ease;
            width: 20%;
        }
        
        /* Update progress based on current question */
        #nav-q1:checked ~ .progress-container .progress-fill { width: 20%; }
        #nav-q2:checked ~ .progress-container .progress-fill { width: 40%; }
        #nav-q3:checked ~ .progress-container .progress-fill { width: 60%; }
        #nav-q4:checked ~ .progress-container .progress-fill { width: 80%; }
        #nav-q5:checked ~ .progress-container .progress-fill { width: 100%; }
        #nav-results:checked ~ .progress-container .progress-fill { width: 100%; }
        
        .question-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 20px;
            line-height: 1.4;
        }
        
        .question-type {
            font-size: 0.9rem;
            color: #888;
            margin-bottom: 15px;
            font-style: italic;
        }
        
        .options-container {
            display: grid;
            gap: 12px;
            margin-bottom: 30px;
        }
        
        .option {
            position: relative;
        }
        
        .option input[type="checkbox"] {
            position: absolute;
            opacity: 0;
            cursor: pointer;
        }
        
        .option-label {
            display: flex;
            align-items: center;
            padding: 16px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            background: #fafafa;
            gap: 12px;
        }
        
        .option-label:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }
        
        .option input[type="checkbox"]:checked + .option-label {
            border-color: #667eea;
            background: #667eea;
            color: white;
        }
        
        .option-emoji {
            font-size: 1.5rem;
            flex-shrink: 0;
        }
        
        .option-text {
            font-size: 1rem;
            font-weight: 500;
            line-height: 1.4;
        }
        
        .navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
        }
        
        .nav-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .nav-btn.primary {
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .nav-btn.primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        
        .nav-btn.secondary {
            background: #f0f0f0;
            color: #666;
        }
        
        .nav-btn.secondary:hover {
            background: #e0e0e0;
        }
        
        .nav-btn:active {
            transform: translateY(0px) scale(0.98);
        }
        
        .nav-btn.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            pointer-events: none;
        }
        
        .question-counter {
            font-size: 0.9rem;
            color: #888;
        }
        
        /* Update question counter */
        #nav-q1:checked ~ .question-slide .question-counter::after { content: "Question 1 of 5"; }
        #nav-q2:checked ~ .question-slide .question-counter::after { content: "Question 2 of 5"; }
        #nav-q3:checked ~ .question-slide .question-counter::after { content: "Question 3 of 5"; }
        #nav-q4:checked ~ .question-slide .question-counter::after { content: "Question 4 of 5"; }
        #nav-q5:checked ~ .question-slide .question-counter::after { content: "Question 5 of 5"; }
        #nav-results:checked ~ .question-slide .question-counter::after { content: "Results"; }
        
        /* CSS-only results calculation */
        .result-section {
            display: none;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        /* Show Game Designer result for creative selections */
        .quiz-container:has(#q1_creative:checked, #q2_creative:checked) .result-game-designer,
        .quiz-container:has(#q1_creative:checked, #q3_creative:checked) .result-game-designer,
        .quiz-container:has(#q2_creative:checked, #q3_creative:checked) .result-game-designer {
            display: block;
            background: #f8f9ff;
        }
        
        /* Show Programmer result for technical selections */
        .quiz-container:has(#q1_technical:checked, #q2_technical:checked) .result-programmer,
        .quiz-container:has(#q1_technical:checked, #q3_technical:checked) .result-programmer,
        .quiz-container:has(#q2_technical:checked, #q3_technical:checked) .result-programmer {
            display: block;
            background: #fff8f0;
        }
        
        /* Show Artist result for visual selections */
        .quiz-container:has(#q1_visual:checked, #q2_visual:checked) .result-artist,
        .quiz-container:has(#q1_visual:checked, #q3_visual:checked) .result-artist,
        .quiz-container:has(#q2_visual:checked, #q3_visual:checked) .result-artist {
            display: block;
            background: #f0fff8;
        }
        
        /* Show Producer result for business selections */
        .quiz-container:has(#q1_business:checked, #q2_business:checked) .result-producer,
        .quiz-container:has(#q1_business:checked, #q3_business:checked) .result-producer,
        .quiz-container:has(#q2_business:checked, #q3_business:checked) .result-producer {
            display: block;
            background: #fff0f8;
        }
        
        /* Default result if no strong pattern */
        .quiz-container:not(:has(.result-section:not(.result-default))) .result-default {
            display: block;
            background: #f8f9ff;
        }
        
        /* Results styling */
        .results-container {
            text-align: center;
            padding: 40px 20px;
        }
        
        .results-title {
            font-size: 2.5rem;
            font-weight: bold;
            color: #1a1a1a;
            margin-bottom: 20px;
        }
        
        .results-role {
            font-size: 2rem;
            font-weight: bold;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 20px;
        }
        
        .results-description {
            font-size: 1.1rem;
            color: #666;
            line-height: 1.6;
            margin-bottom: 30px;
            text-align: left;
        }
        
        /* Book recommendation styles */
        .book-recommendation {
            background: #f8f9ff;
            border: 2px solid #667eea;
            border-radius: 12px;
            padding: 24px;
            margin: 20px 0;
            text-align: left;
        }
        
        .book-header {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            align-items: flex-start;
        }
        
        .book-cover {
            flex-shrink: 0;
            width: 80px;
        }
        
        .book-cover img {
            width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .book-info {
            flex: 1;
        }
        
        .book-title {
            margin: 0 0 8px 0;
            color: #1a1a1a;
            font-size: 1.3rem;
            font-weight: bold;
            line-height: 1.3;
        }
        
        .book-author {
            margin: 0 0 12px 0;
            color: #666;
            font-size: 1.1rem;
            font-style: italic;
        }
        
        .purchase-options {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
            margin: 15px 0;
        }
        
        .purchase-link {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 10px 16px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            font-size: 0.95rem;
            transition: all 0.2s ease;
        }
        
        .purchase-link:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
            color: white;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="quiz-container">
        <div class="quiz-header">
            <h1 class="quiz-title">🎮 2025 Game Dev Career Quiz</h1>
            <p class="quiz-subtitle">Discover how your mind naturally works and what type of game development thinking you have! No experience needed - just answer based on what interests you most. Choose 1-2 answers per question for the best results.</p>
        </div>
        
        <!-- Hidden navigation radio buttons -->
        <input type="radio" name="nav" id="nav-q1" class="nav-radio" checked>
        <input type="radio" name="nav" id="nav-q2" class="nav-radio">
        <input type="radio" name="nav" id="nav-q3" class="nav-radio">
        <input type="radio" name="nav" id="nav-q4" class="nav-radio">
        <input type="radio" name="nav" id="nav-q5" class="nav-radio">
        <input type="radio" name="nav" id="nav-results" class="nav-radio">
        
        <!-- Progress bar -->
        <div class="progress-container">
            <div class="progress-bar">
                <div class="progress-fill"></div>
            </div>
        </div>
