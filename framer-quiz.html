<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-Frame-Options" content="ALLOWALL">
    <title>🎮 2025 Game Dev Career Quiz - Framer Version</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            width: 100%;
            margin: 0;
            padding: 20px;
            background: #ffffff;
            box-sizing: border-box;
        }
        
        .quiz-container {
            width: 100%;
            max-width: none;
            padding: 30px;
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            box-sizing: border-box;
        }
        
        .quiz-title {
            font-size: 2.5rem;
            font-weight: bold;
            color: #1a1a1a;
            margin-bottom: 10px;
            text-align: center;
        }
        
        .quiz-subtitle {
            font-size: 1.1rem;
            color: #666;
            line-height: 1.5;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .progress-container {
            counter-reset: selected-count;
            margin-bottom: 30px;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #f0f0f0;
            border-radius: 4px;
            margin-bottom: 10px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            border-radius: 4px;
            width: 20%;
            transition: width 0.3s ease;
        }
        
        .progress-text {
            text-align: center;
            font-size: 0.9rem;
            color: #888;
        }
        
        .question-container {
            margin-bottom: 30px;
        }
        
        .question-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 20px;
            line-height: 1.4;
        }
        
        .question-type {
            font-size: 0.9rem;
            color: #888;
            margin-bottom: 15px;
            font-style: italic;
        }
        
        .options-container {
            display: grid;
            gap: 12px;
            margin-bottom: 30px;
        }
        
        .option {
            position: relative;
        }
        
        .option input[type="checkbox"] {
            position: absolute;
            opacity: 0;
            cursor: pointer;
        }
        
        .option-label {
            display: flex;
            align-items: center;
            padding: 16px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            background: #fafafa;
            gap: 12px;
        }
        
        .option-label:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }
        
        .option input[type="checkbox"]:checked + .option-label {
            border-color: #667eea;
            background: #667eea;
            color: white;
        }
        
        .option input[type="checkbox"]:checked {
            counter-increment: selected-count;
        }
        
        .option-emoji {
            font-size: 1.5rem;
            flex-shrink: 0;
        }
        
        .option-text {
            font-size: 1rem;
            font-weight: 500;
            line-height: 1.4;
        }
        
        .selection-counter {
            text-align: center;
            margin: 20px 0;
            padding: 10px;
            background: #f8f9ff;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .selection-counter::after {
            content: counter(selected-count);
            background: #667eea;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            margin-left: 8px;
            font-weight: bold;
        }
        
        .navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .btn-primary {
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        
        .btn-primary:active {
            transform: translateY(0px) scale(0.98);
            box-shadow: 0 2px 6px rgba(102, 126, 234, 0.3);
        }
        
        .btn-secondary {
            background: #f0f0f0;
            color: #666;
        }
        
        .btn-secondary:hover {
            background: #e0e0e0;
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .question-counter {
            font-size: 0.9rem;
            color: #888;
        }
        
        /* Button is always enabled since we can't use JavaScript for navigation */
        .btn-primary {
            opacity: 1;
            pointer-events: auto;
        }
        
        /* Feedback sections */
        .feedback {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            display: none;
        }
        
        .option input[type="checkbox"]:checked ~ .feedback {
            display: block;
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
        }
        
        .results-preview {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9ff;
            border-radius: 8px;
            border-left: 4px solid #667eea;
            display: none;
        }
        
        /* Show results preview when enough selections are made */
        .progress-container:has(input:checked) ~ .results-preview {
            display: block;
        }
    </style>
</head>
<body>
    <div class="quiz-container">
        <div class="quiz-header">
            <h1 class="quiz-title">🎮 2025 Game Dev Career Quiz</h1>
            <p class="quiz-subtitle">Discover how your mind naturally works and what type of game development thinking you have! No experience needed - just answer based on what interests you most. Choose 1-2 answers per question for the best results.</p>
        </div>
        
        <div class="progress-container">
            <div class="progress-bar">
                <div class="progress-fill"></div>
            </div>
            <div class="progress-text">Question 1 of 9</div>
        </div>
        
        <form class="quiz-form" id="quizForm">
            <div class="question-container">
                <div class="question-type">Choose up to 2 options</div>
                <h2 class="question-title">What energizes you most when starting a new project?</h2>
                
                <div class="options-container">
                    <div class="option">
                        <input type="checkbox" id="q1_opt1" name="q1" value="creative">
                        <label for="q1_opt1" class="option-label">
                            <span class="option-emoji">🎨</span>
                            <span class="option-text">Sketching out the big picture and overall vision</span>
                        </label>
                    </div>
                    
                    <div class="option">
                        <input type="checkbox" id="q1_opt2" name="q1" value="technical">
                        <label for="q1_opt2" class="option-label">
                            <span class="option-emoji">⚙️</span>
                            <span class="option-text">Diving deep into technical architecture and problem-solving</span>
                        </label>
                    </div>
                    
                    <div class="option">
                        <input type="checkbox" id="q1_opt3" name="q1" value="visual">
                        <label for="q1_opt3" class="option-label">
                            <span class="option-emoji">🎭</span>
                            <span class="option-text">Creating visual concepts and mood boards</span>
                        </label>
                    </div>
                    
                    <div class="option">
                        <input type="checkbox" id="q1_opt4" name="q1" value="user">
                        <label for="q1_opt4" class="option-label">
                            <span class="option-emoji">👥</span>
                            <span class="option-text">Understanding the audience and their needs</span>
                        </label>
                    </div>
                    
                    <div class="option">
                        <input type="checkbox" id="q1_opt5" name="q1" value="narrative">
                        <label for="q1_opt5" class="option-label">
                            <span class="option-emoji">📖</span>
                            <span class="option-text">Planning the narrative and emotional journey</span>
                        </label>
                    </div>
                </div>
                
                <div class="selection-counter">
                    Selections made: 
                </div>
                
                <div class="feedback">
                    <p>✅ Great choice! Your selections are helping us understand your natural thinking style.</p>
                </div>
            </div>
            
            <div class="navigation">
                <button type="button" class="btn btn-secondary" style="visibility: hidden;">Previous</button>
                <span class="question-counter">Question 1 of 9</span>
                <button type="button" class="btn btn-primary">Next Question</button>
            </div>
        </form>
        
        <div class="results-preview">
            <h3>🎯 Preview: Based on your selections so far...</h3>
            <p>You're showing strong interest in <strong>creative and strategic thinking</strong>. This suggests you might think like a <strong>Creative Director</strong> or <strong>Game Designer</strong>!</p>
            <p><em>Complete all questions for your full personalized results and book recommendation.</em></p>
        </div>
    </div>
</body>
</html>
