<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-Frame-Options" content="ALLOWALL">
    <title>CSS-Only Test for Framer</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        /* CSS-only toggle using checkbox hack */
        .css-toggle {
            position: relative;
            margin: 10px 0;
        }
        
        .css-toggle input[type="checkbox"] {
            position: absolute;
            opacity: 0;
            cursor: pointer;
        }
        
        .css-toggle-label {
            display: flex;
            align-items: center;
            padding: 16px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            background: #fafafa;
            gap: 12px;
        }
        
        .css-toggle-label:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }
        
        .css-toggle input[type="checkbox"]:checked + .css-toggle-label {
            border-color: #667eea;
            background: #667eea;
            color: white;
        }
        
        /* CSS-only accordion/dropdown */
        .css-accordion {
            margin: 20px 0;
        }
        
        .css-accordion input[type="checkbox"] {
            position: absolute;
            opacity: 0;
        }
        
        .css-accordion-label {
            display: block;
            padding: 16px 20px;
            background: #667eea;
            color: white;
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.2s ease;
        }
        
        .css-accordion-label:hover {
            background: #5a6fd8;
        }
        
        .css-accordion-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background: #f8f9ff;
            border: 2px solid #667eea;
            border-top: none;
            border-radius: 0 0 8px 8px;
        }
        
        .css-accordion input[type="checkbox"]:checked ~ .css-accordion-content {
            max-height: 200px;
            padding: 20px;
        }
        
        /* CSS-only tabs */
        .css-tabs {
            margin: 20px 0;
        }
        
        .css-tabs input[type="radio"] {
            position: absolute;
            opacity: 0;
        }
        
        .css-tab-labels {
            display: flex;
            gap: 2px;
        }
        
        .css-tab-label {
            padding: 12px 20px;
            background: #e0e0e0;
            cursor: pointer;
            border-radius: 8px 8px 0 0;
            transition: all 0.2s ease;
        }
        
        .css-tab-label:hover {
            background: #d0d0d0;
        }
        
        .css-tabs input[type="radio"]:checked + .css-tab-label {
            background: #667eea;
            color: white;
        }
        
        .css-tab-content {
            display: none;
            padding: 20px;
            background: #f8f9ff;
            border: 2px solid #667eea;
            border-radius: 0 8px 8px 8px;
        }
        
        .css-tabs input[type="radio"]:checked ~ .css-tab-content {
            display: block;
        }
        
        /* Status indicator using CSS counters */
        .counter-container {
            counter-reset: selections;
            margin: 20px 0;
        }
        
        .css-toggle input[type="checkbox"]:checked {
            counter-increment: selections;
        }
        
        .selection-count::after {
            content: counter(selections);
            background: #667eea;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            margin-left: 8px;
        }
        
        .info-box {
            padding: 15px;
            background: #e8f5e8;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #4caf50;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 CSS-Only Test for Framer</h1>
        <p>This uses pure CSS interactions that should work even if JavaScript is blocked.</p>
        
        <div class="counter-container">
            <h3>Test 1: CSS Toggle Options <span class="selection-count">Selected: </span></h3>
            
            <div class="css-toggle">
                <input type="checkbox" id="option1">
                <label for="option1" class="css-toggle-label">
                    <span>🎨</span>
                    <span>CSS Toggle Option 1</span>
                </label>
            </div>
            
            <div class="css-toggle">
                <input type="checkbox" id="option2">
                <label for="option2" class="css-toggle-label">
                    <span>⚙️</span>
                    <span>CSS Toggle Option 2</span>
                </label>
            </div>
            
            <div class="css-toggle">
                <input type="checkbox" id="option3">
                <label for="option3" class="css-toggle-label">
                    <span>🎭</span>
                    <span>CSS Toggle Option 3</span>
                </label>
            </div>
        </div>
        
        <h3>Test 2: CSS Accordion</h3>
        <div class="css-accordion">
            <input type="checkbox" id="accordion1">
            <label for="accordion1" class="css-accordion-label">
                Click to expand/collapse
            </label>
            <div class="css-accordion-content">
                <p>This content appears when you click the header above. It's pure CSS - no JavaScript required!</p>
                <p>This proves that CSS-based interactions work in Framer embeds.</p>
            </div>
        </div>
        
        <h3>Test 3: CSS Tabs</h3>
        <div class="css-tabs">
            <div class="css-tab-labels">
                <input type="radio" name="tabs" id="tab1" checked>
                <label for="tab1" class="css-tab-label">Tab 1</label>
                
                <input type="radio" name="tabs" id="tab2">
                <label for="tab2" class="css-tab-label">Tab 2</label>
                
                <input type="radio" name="tabs" id="tab3">
                <label for="tab3" class="css-tab-label">Tab 3</label>
            </div>
            
            <div class="css-tab-content">
                <h4>Tab 1 Content</h4>
                <p>This is the content for tab 1. Click other tabs to see different content.</p>
            </div>
            
            <div class="css-tab-content">
                <h4>Tab 2 Content</h4>
                <p>This is the content for tab 2. All switching is done with pure CSS.</p>
            </div>
            
            <div class="css-tab-content">
                <h4>Tab 3 Content</h4>
                <p>This is the content for tab 3. No JavaScript needed!</p>
            </div>
        </div>
        
        <div class="info-box">
            <h4>💡 How This Works</h4>
            <p>This demo uses CSS-only techniques:</p>
            <ul>
                <li><strong>Checkbox hack</strong> - Hidden checkboxes with styled labels</li>
                <li><strong>CSS counters</strong> - Automatically count selections</li>
                <li><strong>:checked pseudo-class</strong> - Style based on form state</li>
                <li><strong>Adjacent sibling selectors</strong> - Show/hide content</li>
            </ul>
            <p>These techniques work even when JavaScript is disabled or blocked by iframe restrictions.</p>
        </div>
    </div>
</body>
</html>
