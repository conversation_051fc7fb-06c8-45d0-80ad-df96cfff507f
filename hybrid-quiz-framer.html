<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-Frame-Options" content="ALLOWALL">
    <title>🎮 2025 Game Dev Career Quiz</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            width: 100%;
            margin: 0;
            padding: 20px;
            background: #ffffff;
            box-sizing: border-box;
        }
        
        .quiz-container {
            width: 100%;
            max-width: none;
            padding: 30px;
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            box-sizing: border-box;
        }
        
        .quiz-title {
            font-size: 2.5rem;
            font-weight: bold;
            color: #1a1a1a;
            margin-bottom: 10px;
            text-align: center;
        }
        
        .quiz-subtitle {
            font-size: 1.1rem;
            color: #666;
            line-height: 1.5;
            text-align: center;
            margin-bottom: 30px;
        }
        
        /* CSS-only navigation system */
        .nav-radio {
            position: absolute;
            opacity: 0;
            pointer-events: none;
        }
        
        .question-slide {
            display: none;
            animation: fadeIn 0.3s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateX(20px); }
            to { opacity: 1; transform: translateX(0); }
        }
        
        /* Show questions based on navigation state */
        #nav-q1:checked ~ .question-slide.q1 { display: block; }
        #nav-q2:checked ~ .question-slide.q2 { display: block; }
        #nav-q3:checked ~ .question-slide.q3 { display: block; }
        #nav-results:checked ~ .question-slide.results { display: block; }
        
        /* Progress bar */
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #f0f0f0;
            border-radius: 4px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            border-radius: 4px;
            transition: width 0.3s ease;
            width: 33%;
        }
        
        /* Update progress based on current question */
        #nav-q1:checked ~ .progress-container .progress-fill { width: 33%; }
        #nav-q2:checked ~ .progress-container .progress-fill { width: 66%; }
        #nav-q3:checked ~ .progress-container .progress-fill { width: 100%; }
        #nav-results:checked ~ .progress-container .progress-fill { width: 100%; }
        
        .question-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 20px;
            line-height: 1.4;
        }
        
        .question-type {
            font-size: 0.9rem;
            color: #888;
            margin-bottom: 15px;
            font-style: italic;
        }
        
        .options-container {
            display: grid;
            gap: 12px;
            margin-bottom: 30px;
        }
        
        .option {
            position: relative;
        }
        
        .option input[type="checkbox"] {
            position: absolute;
            opacity: 0;
            cursor: pointer;
        }
        
        .option-label {
            display: flex;
            align-items: center;
            padding: 16px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            background: #fafafa;
            gap: 12px;
        }
        
        .option-label:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }
        
        .option input[type="checkbox"]:checked + .option-label {
            border-color: #667eea;
            background: #667eea;
            color: white;
        }
        
        .option-emoji {
            font-size: 1.5rem;
            flex-shrink: 0;
        }
        
        .option-text {
            font-size: 1rem;
            font-weight: 500;
            line-height: 1.4;
        }
        
        .navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
        }
        
        .nav-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .nav-btn.primary {
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .nav-btn.primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        
        .nav-btn.secondary {
            background: #f0f0f0;
            color: #666;
        }
        
        .nav-btn.secondary:hover {
            background: #e0e0e0;
        }
        
        .nav-btn:active {
            transform: translateY(0px) scale(0.98);
        }
        
        .nav-btn.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            pointer-events: none;
        }
        
        .question-counter {
            font-size: 0.9rem;
            color: #888;
        }
        
        /* Update question counter */
        #nav-q1:checked ~ .question-slide .question-counter::after { content: "Question 1 of 3"; }
        #nav-q2:checked ~ .question-slide .question-counter::after { content: "Question 2 of 3"; }
        #nav-q3:checked ~ .question-slide .question-counter::after { content: "Question 3 of 3"; }
        #nav-results:checked ~ .question-slide .question-counter::after { content: "Results"; }
        
        /* Results styling */
        .results-container {
            text-align: center;
            padding: 40px 20px;
        }
        
        .results-title {
            font-size: 2.5rem;
            font-weight: bold;
            color: #1a1a1a;
            margin-bottom: 20px;
        }
        
        .results-role {
            font-size: 2rem;
            font-weight: bold;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 20px;
        }
        
        .results-description {
            font-size: 1.1rem;
            color: #666;
            line-height: 1.6;
            margin-bottom: 30px;
            text-align: left;
        }
        
        /* Book recommendation styles */
        .book-recommendation {
            background: #f8f9ff;
            border: 2px solid #667eea;
            border-radius: 12px;
            padding: 24px;
            margin: 20px 0;
            text-align: left;
        }
        
        .book-header {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            align-items: flex-start;
        }
        
        .book-cover {
            flex-shrink: 0;
            width: 80px;
        }
        
        .book-cover img {
            width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .book-info {
            flex: 1;
        }
        
        .book-title {
            margin: 0 0 8px 0;
            color: #1a1a1a;
            font-size: 1.3rem;
            font-weight: bold;
            line-height: 1.3;
        }
        
        .book-author {
            margin: 0 0 12px 0;
            color: #666;
            font-size: 1.1rem;
            font-style: italic;
        }
        
        .book-meta {
            display: flex;
            gap: 16px;
            flex-wrap: wrap;
            font-size: 0.9rem;
            color: #888;
        }
        
        .book-meta span {
            background: #e8f0fe;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: 500;
        }
        
        .purchase-options {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
            margin: 15px 0;
        }
        
        .purchase-link {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 10px 16px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            font-size: 0.95rem;
            transition: all 0.2s ease;
        }
        
        .purchase-link:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
            color: white;
            text-decoration: none;
        }
        
        .live-preview {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9ff;
            border-radius: 8px;
            border-left: 4px solid #667eea;
            display: none;
        }
        
        /* Show preview when selections are made */
        .quiz-container:has(input[type="checkbox"]:checked) .live-preview {
            display: block;
        }
    </style>
</head>
<body>
    <div class="quiz-container">
        <div class="quiz-header">
            <h1 class="quiz-title">🎮 2025 Game Dev Career Quiz</h1>
            <p class="quiz-subtitle">Discover how your mind naturally works and what type of game development thinking you have! No experience needed - just answer based on what interests you most. Choose 1-2 answers per question for the best results.</p>
        </div>
        
        <!-- Hidden navigation radio buttons -->
        <input type="radio" name="nav" id="nav-q1" class="nav-radio" checked>
        <input type="radio" name="nav" id="nav-q2" class="nav-radio">
        <input type="radio" name="nav" id="nav-q3" class="nav-radio">
        <input type="radio" name="nav" id="nav-results" class="nav-radio">
        
        <!-- Progress bar -->
        <div class="progress-container">
            <div class="progress-bar">
                <div class="progress-fill"></div>
            </div>
        </div>

        <!-- Question 1 -->
        <div class="question-slide q1">
            <div class="question-type">Choose up to 2 options</div>
            <h2 class="question-title">When you imagine starting a creative project, what excites you most?</h2>
            <div class="options-container">
                <div class="option">
                    <input type="checkbox" id="q1_opt1" name="q1" data-scores='{"creative_score": 3, "business_score": 1, "leadership_preference": 2}'>
                    <label for="q1_opt1" class="option-label">
                        <span class="option-emoji">🎨</span>
                        <span class="option-text">Imagining the big picture and how everything will look and feel</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q1_opt2" name="q1" data-scores='{"technical_score": 3, "systems_score": 2, "detail_orientation": 2}'>
                    <label for="q1_opt2" class="option-label">
                        <span class="option-emoji">⚙️</span>
                        <span class="option-text">Figuring out how things work and solving complex puzzles</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q1_opt3" name="q1" data-scores='{"visual_score": 3, "creative_score": 1, "innovation_drive": 1}'>
                    <label for="q1_opt3" class="option-label">
                        <span class="option-emoji">🎭</span>
                        <span class="option-text">Creating beautiful visuals and artistic concepts</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q1_opt4" name="q1" data-scores='{"business_score": 2, "social_score": 2, "systems_score": 1, "user_focus": 3}'>
                    <label for="q1_opt4" class="option-label">
                        <span class="option-emoji">👥</span>
                        <span class="option-text">Understanding what people want and how they'll react</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q1_opt5" name="q1" data-scores='{"narrative_score": 3, "creative_score": 1, "user_focus": 2}'>
                    <label for="q1_opt5" class="option-label">
                        <span class="option-emoji">📖</span>
                        <span class="option-text">Crafting stories and emotional experiences</span>
                    </label>
                </div>
            </div>
            <div class="navigation">
                <span class="nav-btn secondary disabled">Previous</span>
                <span class="question-counter"></span>
                <label for="nav-q2" class="nav-btn primary">Next</label>
            </div>
        </div>

        <!-- Question 2 -->
        <div class="question-slide q2">
            <div class="question-type">Choose up to 2 options</div>
            <h2 class="question-title">How do you prefer to receive feedback?</h2>
            <div class="options-container">
                <div class="option">
                    <input type="checkbox" id="q2_opt1" name="q2" data-scores='{"technical_score": 3, "detail_orientation": 2, "collaboration_style": 1}'>
                    <label for="q2_opt1" class="option-label">
                        <span class="option-emoji">💻</span>
                        <span class="option-text">Technical peer review with specific implementation suggestions</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q2_opt2" name="q2" data-scores='{"creative_score": 2, "visual_score": 2, "audio_score": 1, "innovation_drive": 2}'>
                    <label for="q2_opt2" class="option-label">
                        <span class="option-emoji">🎨</span>
                        <span class="option-text">Creative direction and artistic vision guidance</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q2_opt3" name="q2" data-scores='{"narrative_score": 2, "social_score": 2, "user_focus": 3}'>
                    <label for="q2_opt3" class="option-label">
                        <span class="option-emoji">❤️</span>
                        <span class="option-text">User reactions and emotional responses to your work</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q2_opt4" name="q2" data-scores='{"business_score": 3, "systems_score": 2, "detail_orientation": 2}'>
                    <label for="q2_opt4" class="option-label">
                        <span class="option-emoji">📈</span>
                        <span class="option-text">Data-driven insights and performance metrics</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q2_opt5" name="q2" data-scores='{"creative_score": 2, "technical_score": 1, "innovation_drive": 3}'>
                    <label for="q2_opt5" class="option-label">
                        <span class="option-emoji">🚀</span>
                        <span class="option-text">Recognition for innovation and pushing boundaries</span>
                    </label>
                </div>
            </div>
            <div class="navigation">
                <label for="nav-q1" class="nav-btn secondary">Previous</label>
                <span class="question-counter"></span>
                <label for="nav-q3" class="nav-btn primary">Next</label>
            </div>
        </div>

        <!-- Question 3 -->
        <div class="question-slide q3">
            <div class="question-type">Choose up to 2 options</div>
            <h2 class="question-title">What motivates you most in your career?</h2>
            <div class="options-container">
                <div class="option">
                    <input type="checkbox" id="q3_opt1" name="q3" data-scores='{"technical_score": 3, "innovation_drive": 2, "detail_orientation": 2}'>
                    <label for="q3_opt1" class="option-label">
                        <span class="option-emoji">🧩</span>
                        <span class="option-text">Solving complex challenges that others can't</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q3_opt2" name="q3" data-scores='{"creative_score": 3, "visual_score": 2, "innovation_drive": 1}'>
                    <label for="q3_opt2" class="option-label">
                        <span class="option-emoji">🎨</span>
                        <span class="option-text">Bringing creative visions to life</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q3_opt3" name="q3" data-scores='{"narrative_score": 2, "social_score": 2, "user_focus": 3}'>
                    <label for="q3_opt3" class="option-label">
                        <span class="option-emoji">❤️</span>
                        <span class="option-text">Creating experiences that emotionally impact people</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q3_opt4" name="q3" data-scores='{"business_score": 3, "systems_score": 2, "leadership_preference": 2}'>
                    <label for="q3_opt4" class="option-label">
                        <span class="option-emoji">📈</span>
                        <span class="option-text">Building products that reach millions</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q3_opt5" name="q3" data-scores='{"business_score": 2, "social_score": 2, "leadership_preference": 3}'>
                    <label for="q3_opt5" class="option-label">
                        <span class="option-emoji">👑</span>
                        <span class="option-text">Leading teams and shaping company direction</span>
                    </label>
                </div>
            </div>
            <div class="navigation">
                <label for="nav-q2" class="nav-btn secondary">Previous</label>
                <span class="question-counter"></span>
                <label for="nav-results" class="nav-btn primary">See Results</label>
            </div>
        </div>

        <!-- Results Section -->
        <div class="question-slide results">
            <div class="results-container">
                <h2 class="results-title">🎉 Your Game Dev Career Results</h2>

                <div class="results-role" id="resultRole">You think like a Game Designer!</div>

                <div class="results-description" id="resultDescription">
                    <p>Based on your responses, you have a natural inclination toward big-picture thinking, creative problem-solving, and bringing innovative concepts to life. You're drawn to the artistic and conceptual side of game development, with a strong focus on user experience and emotional impact.</p>
                </div>

                <!-- Book Recommendation Section -->
                <div class="book-recommendation" id="bookRecommendation">
                    <h3>📚 Recommended Reading:</h3>
                    <div class="book-header">
                        <div class="book-cover">
                            <img id="bookCover" src="https://images-na.ssl-images-amazon.com/images/P/0881341177.01.L.jpg" alt="Book cover" loading="lazy">
                        </div>
                        <div class="book-info">
                            <h4 class="book-title" id="bookTitle">"The Art of Computer Game Design"</h4>
                            <p class="book-author" id="bookAuthor">by Chris Crawford</p>
                            <div class="book-meta">
                                <span id="bookTime">3-5 hours</span>
                                <span id="bookDifficulty">Beginner</span>
                            </div>
                        </div>
                    </div>

                    <div class="book-description">
                        <p id="bookWhy"><strong>Why this book is essential:</strong> The foundational text on game design philosophy. Crawford's insights into what makes games engaging and meaningful remain relevant decades later. Essential reading for understanding the art and craft of game design.</p>
                    </div>

                    <div class="purchase-options" id="purchaseOptions">
                        <a href="https://amzn.to/4krS071" target="_blank" class="purchase-link">📖 Paperback</a>
                        <a href="https://amzn.to/4nKw2PO" target="_blank" class="purchase-link">📱 Kindle</a>
                        <a href="https://amzn.to/44CnNwf" target="_blank" class="purchase-link">📚 Hardcover</a>
                    </div>

                    <div style="margin-top: 15px; padding: 10px; background: #e8f5e8; border-radius: 6px; font-size: 0.9rem; color: #2e7d32;">
                        💡 <strong>Free Option:</strong> <span id="freeOption">Available free online at Chris Crawford's website</span>
                    </div>

                    <div style="margin-top: 15px; font-size: 0.85rem; color: #666; font-style: italic;">
                        📚 As an Amazon Associate, I earn from qualifying purchases. This helps support the creation of more career resources like this quiz!
                    </div>
                </div>

                <div style="margin-top: 30px; padding: 20px; background: #e8f5e8; border-radius: 8px; border-left: 4px solid #4caf50; text-align: left;">
                    <p style="margin: 0; color: #2e7d32; line-height: 1.6;">
                        🌟 <strong>Remember:</strong> The game industry values diverse perspectives and creative thinking. Your unique combination of interests and natural inclinations is exactly what makes great games possible. Start building, start creating, and don't be afraid to share your ideas with the world!
                    </p>
                </div>

                <div class="navigation">
                    <label for="nav-q1" class="nav-btn primary">Take Quiz Again</label>
                </div>
            </div>
        </div>

        <!-- Live Preview Section -->
        <div class="live-preview">
            <h3>🎯 Preview: Based on your selections so far...</h3>
            <p id="previewText">You're showing strong interest in creative and strategic thinking. This suggests you might think like a <strong>Creative Director</strong> or <strong>Game Designer</strong>!</p>
            <p><em>Complete all questions for your full personalized results and book recommendation.</em></p>
        </div>
    </div>

    <script>
        // Enhanced Book Database with Real Affiliate Links from your resources
        const bookDatabase = {
            "The Art of Computer Game Design": {
                author: "Chris Crawford",
                amazonLinks: {
                    paperback: "https://amzn.to/4krS071",
                    kindle: "https://amzn.to/4nKw2PO",
                    hardcover: "https://amzn.to/44CnNwf"
                },
                whyEssential: "The foundational text on game design philosophy. Crawford's insights into what makes games engaging and meaningful remain relevant decades later. Essential reading for understanding the art and craft of game design.",
                difficulty: "Beginner",
                timeToRead: "3-5 hours",
                freeVersion: "Available free online at Chris Crawford's website",
                amazonASIN: "0881341177"
            },
            "Game Programming Patterns": {
                author: "Robert Nystrom",
                amazonLinks: {
                    paperback: "https://amzn.to/46yfa8s",
                    kindle: "https://amzn.to/4ksEjVu"
                },
                whyEssential: "The perfect bridge between general programming patterns and game-specific implementations. Nystrom explains complex concepts with clear examples and practical game development scenarios. A must-read for any gameplay programmer.",
                difficulty: "Beginner to Intermediate",
                timeToRead: "10-15 hours",
                freeVersion: "Available free online at gameprogrammingpatterns.com",
                amazonASIN: "0990582906"
            },
            "The Visual Story": {
                author: "Bruce Block",
                amazonLinks: {
                    paperback: "https://amzn.to/4eH8LtF",
                    etextbook: "https://amzn.to/3TwrcYA",
                    hardcover: "https://amzn.to/4lL7UdU"
                },
                whyEssential: "Essential for understanding visual storytelling and composition. While focused on film, the principles apply directly to game cinematics, level design, and visual communication. Perfect for art directors and visual designers.",
                difficulty: "Intermediate",
                timeToRead: "8-12 hours",
                amazonASIN: "113801415X"
            },
            "Color and Light": {
                author: "James Gurney",
                amazonLinks: {
                    paperback: "https://amzn.to/3GvFNR6",
                    spiralbound: "https://amzn.to/3GFi68N"
                },
                whyEssential: "The definitive guide to understanding color and lighting for artists. Gurney's practical approach to color theory and lighting principles is invaluable for game artists working in any medium.",
                difficulty: "Intermediate",
                timeToRead: "6-10 hours",
                amazonASIN: "0740797719"
            },
            "Blood, Sweat, and Pixels": {
                author: "Jason Schreier",
                amazonLinks: {
                    kindle: "https://amzn.to/3GBCsjm",
                    audiobook: "https://amzn.to/3GBCsjm",
                    paperback: "https://amzn.to/4eJ38v5"
                },
                whyEssential: "Essential reading for understanding the realities of game development. Schreier's behind-the-scenes look at major game productions reveals the challenges, triumphs, and human cost of making games.",
                difficulty: "Beginner",
                timeToRead: "8-12 hours",
                amazonASIN: "0062651234"
            }
        };

        // Role to Book Mapping
        const roleBookMapping = {
            "Game Designer": "The Art of Computer Game Design",
            "Creative Director": "The Art of Computer Game Design",
            "Lead Game Designer": "The Art of Computer Game Design",
            "Gameplay Programmer": "Game Programming Patterns",
            "Technical Artist": "Game Programming Patterns",
            "Art Director": "The Visual Story",
            "Visual Artist": "Color and Light",
            "Producer": "Blood, Sweat, and Pixels",
            "Executive Producer": "Blood, Sweat, and Pixels"
        };

        let scores = {
            creative_score: 0,
            technical_score: 0,
            visual_score: 0,
            audio_score: 0,
            social_score: 0,
            business_score: 0,
            narrative_score: 0,
            systems_score: 0,
            leadership_preference: 0,
            collaboration_style: 0,
            detail_orientation: 0,
            innovation_drive: 0,
            user_focus: 0
        };

        // Calculate results and show them
        function calculateResults() {
            // Reset scores
            Object.keys(scores).forEach(key => scores[key] = 0);

            // Calculate scores from all checked inputs
            const checkedInputs = document.querySelectorAll('input[type="checkbox"]:checked');
            console.log('Checked inputs:', checkedInputs.length);

            checkedInputs.forEach(input => {
                const inputScores = JSON.parse(input.dataset.scores);
                console.log('Input scores:', inputScores);
                Object.keys(inputScores).forEach(scoreType => {
                    if (scores.hasOwnProperty(scoreType)) {
                        scores[scoreType] += inputScores[scoreType];
                    }
                });
            });

            console.log('Final scores:', scores);

            // Determine primary role
            const role = determineRole();
            console.log('Determined role:', role);
            const bookTitle = roleBookMapping[role] || "The Art of Computer Game Design";
            const book = bookDatabase[bookTitle];

            // Update results display
            updateResultsDisplay(role, book, bookTitle);

            // Navigate to results
            document.getElementById('nav-results').checked = true;
        }

        function determineRole() {
            // Find the highest scoring category
            const categoryScores = {
                technical_score: scores.technical_score,
                creative_score: scores.creative_score,
                visual_score: scores.visual_score,
                business_score: scores.business_score,
                narrative_score: scores.narrative_score
            };

            console.log('Category scores:', categoryScores);

            const primaryCategory = Object.keys(categoryScores).reduce((a, b) =>
                categoryScores[a] > categoryScores[b] ? a : b
            );

            console.log('Primary category:', primaryCategory, 'with score:', categoryScores[primaryCategory]);

            // Determine specific role based on primary category and secondary factors
            if (primaryCategory === 'technical_score') {
                if (scores.leadership_preference >= 4) {
                    return "Technical Director";
                } else {
                    return "Gameplay Programmer";
                }
            } else if (primaryCategory === 'creative_score') {
                if (scores.visual_score >= 4) {
                    return "Art Director";
                } else if (scores.leadership_preference >= 3) {
                    return "Creative Director";
                } else {
                    return "Game Designer";
                }
            } else if (primaryCategory === 'visual_score') {
                if (scores.technical_score >= 3) {
                    return "Technical Artist";
                } else if (scores.leadership_preference >= 3) {
                    return "Art Director";
                } else {
                    return "Visual Artist";
                }
            } else if (primaryCategory === 'business_score') {
                if (scores.leadership_preference >= 4) {
                    return "Executive Producer";
                } else {
                    return "Producer";
                }
            } else if (primaryCategory === 'narrative_score') {
                return "Narrative Designer";
            } else {
                return "Game Designer"; // Default fallback
            }
        }

        function updateResultsDisplay(role, book, bookTitle) {
            // Update role
            document.getElementById('resultRole').textContent = `You think like a ${role}!`;

            // Update description based on role
            const descriptions = {
                "Game Designer": "You're the architect of fun - the person who crafts the rules, systems, and interactions that make games engaging. Whether designing levels like those in Portal, mechanics like those in Tetris, or progression systems like those in RPGs, you understand what makes players tick.",
                "Creative Director": "You're the creative mastermind who ensures all elements serve a unified vision. Think of directors like Amy Hennig (Uncharted), Ken Levine (BioShock), or Hideo Kojima (Metal Gear). You see how gameplay, story, art, and sound weave together to create experiences that players remember for years.",
                "Technical Director": "You're the technical leader who makes impossible things possible. Like the technical directors behind games like The Last of Us Part II, Red Dead Redemption 2, or Cyberpunk 2077, you solve the hardest problems and guide teams through complex technical challenges.",
                "Gameplay Programmer": "You're the programmer who makes games fun. You implement the mechanics that players interact with directly - combat systems, character controllers, UI responsiveness. Your code is the bridge between design documents and player joy.",
                "Visual Artist": "You're the artist who brings game worlds to life through concept art, 3D models, textures, or animations. Whether creating the atmospheric environments of Hollow Knight, the character designs of Overwatch, or the visual effects of Diablo, your work directly shapes what players see and feel.",
                "Art Director": "You're the visual architect who defines how games look and feel. Like the art directors behind Journey, Ori and the Blind Forest, or Cuphead, you shape entire aesthetic experiences. You understand that art isn't just decoration - it's communication, emotion, and world-building all rolled into one.",
                "Technical Artist": "You're the bridge between art and code - the person who makes impossible visual ideas actually work in-engine. Like the technical artists who figured out how to make Breath of the Wild's art style run on Switch, or how to create Overwatch's stylized lighting system.",
                "Producer": "You're the project coordinator who keeps game development on track. Like the producers behind successful AAA releases, you manage timelines, resources, and team dynamics to ensure games ship on time and on budget while maintaining quality and team sanity.",
                "Executive Producer": "You're the strategic leader who balances creative vision with business reality. Like the executive producers behind franchises like Assassin's Creed, Call of Duty, or The Witcher, you ensure great games actually reach players while maintaining quality and team morale.",
                "Narrative Designer": "You're the storyteller who creates interactive narratives that respond to player choices. Like the designers behind Disco Elysium, The Stanley Parable, or Mass Effect, you understand that game stories aren't just told - they're experienced, shaped by player agency and meaningful choices."
            };

            document.getElementById('resultDescription').innerHTML = `<p>${descriptions[role] || descriptions["Game Designer"]}</p>`;

            // Update book recommendation
            if (book) {
                document.getElementById('bookTitle').textContent = `"${bookTitle}"`;
                document.getElementById('bookAuthor').textContent = `by ${book.author}`;
                document.getElementById('bookTime').textContent = book.timeToRead;
                document.getElementById('bookDifficulty').textContent = book.difficulty;
                document.getElementById('bookWhy').innerHTML = `<strong>Why this book is essential:</strong> ${book.whyEssential}`;
                document.getElementById('bookCover').src = `https://images-na.ssl-images-amazon.com/images/P/${book.amazonASIN}.01.L.jpg`;

                // Update purchase links
                const purchaseOptions = document.getElementById('purchaseOptions');
                purchaseOptions.innerHTML = '';

                const formatIcons = {
                    paperback: '📖',
                    hardcover: '📚',
                    kindle: '📱',
                    audiobook: '🎧',
                    etextbook: '💻',
                    spiralbound: '📖'
                };

                Object.entries(book.amazonLinks).forEach(([format, url]) => {
                    const icon = formatIcons[format] || '📄';
                    const formatName = format.charAt(0).toUpperCase() + format.slice(1);
                    const link = document.createElement('a');
                    link.href = url;
                    link.target = '_blank';
                    link.className = 'purchase-link';
                    link.innerHTML = `${icon} ${formatName}`;
                    purchaseOptions.appendChild(link);
                });

                // Update free option
                const freeOption = document.getElementById('freeOption');
                if (book.freeVersion) {
                    freeOption.textContent = book.freeVersion;
                    freeOption.parentElement.style.display = 'block';
                } else {
                    freeOption.parentElement.style.display = 'none';
                }
            }
        }

        // Update live preview as user makes selections
        function updatePreview() {
            const checkedInputs = document.querySelectorAll('input[type="checkbox"]:checked');
            if (checkedInputs.length === 0) return;

            // Quick preview calculation
            let tempScores = { creative_score: 0, technical_score: 0, visual_score: 0, business_score: 0 };
            checkedInputs.forEach(input => {
                const inputScores = JSON.parse(input.dataset.scores);
                Object.keys(tempScores).forEach(scoreType => {
                    if (inputScores[scoreType]) {
                        tempScores[scoreType] += inputScores[scoreType];
                    }
                });
            });

            const topScore = Object.keys(tempScores).reduce((a, b) =>
                tempScores[a] > tempScores[b] ? a : b
            );

            let previewRole = "Creative Thinker";
            if (topScore === 'technical_score') previewRole = "Technical Problem Solver";
            else if (topScore === 'visual_score') previewRole = "Visual Creator";
            else if (topScore === 'business_score') previewRole = "Strategic Leader";

            document.getElementById('previewText').innerHTML =
                `You're showing strong interest in ${previewRole.toLowerCase()} thinking. This suggests you might think like a <strong>${previewRole}</strong>!`;
        }

        // Add event listeners
        document.addEventListener('DOMContentLoaded', function() {
            const checkboxes = document.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', updatePreview);
            });

            // Listen for results navigation
            const resultsRadio = document.getElementById('nav-results');
            resultsRadio.addEventListener('change', function() {
                if (this.checked) {
                    calculateResults();
                }
            });
        });
    </script>
</body>
</html>
