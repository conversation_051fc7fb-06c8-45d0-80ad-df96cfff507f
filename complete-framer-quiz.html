<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-Frame-Options" content="ALLOWALL">
    <title>🎮 2025 Game Dev Career Quiz</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            width: 100%;
            margin: 0;
            padding: 20px;
            background: #ffffff;
            box-sizing: border-box;
        }
        
        .quiz-container {
            width: 100%;
            max-width: none;
            padding: 30px;
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            box-sizing: border-box;
        }
        
        .quiz-title {
            font-size: 2.5rem;
            font-weight: bold;
            color: #1a1a1a;
            margin-bottom: 10px;
            text-align: center;
        }
        
        .quiz-subtitle {
            font-size: 1.1rem;
            color: #666;
            line-height: 1.5;
            text-align: center;
            margin-bottom: 30px;
        }
        
        /* CSS-only navigation system */
        .nav-radio {
            position: absolute;
            opacity: 0;
            pointer-events: none;
        }
        
        .question-slide {
            display: none;
            animation: fadeIn 0.3s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateX(20px); }
            to { opacity: 1; transform: translateX(0); }
        }
        
        /* Show questions based on navigation state */
        #nav-q1:checked ~ .question-slide.q1 { display: block; }
        #nav-q2:checked ~ .question-slide.q2 { display: block; }
        #nav-q3:checked ~ .question-slide.q3 { display: block; }
        #nav-q4:checked ~ .question-slide.q4 { display: block; }
        #nav-q5:checked ~ .question-slide.q5 { display: block; }
        #nav-q6:checked ~ .question-slide.q6 { display: block; }
        #nav-q7:checked ~ .question-slide.q7 { display: block; }
        #nav-q8:checked ~ .question-slide.q8 { display: block; }
        #nav-q9:checked ~ .question-slide.q9 { display: block; }
        #nav-q10:checked ~ .question-slide.q10 { display: block; }
        #nav-q11:checked ~ .question-slide.q11 { display: block; }
        #nav-q12:checked ~ .question-slide.q12 { display: block; }
        #nav-results:checked ~ .question-slide.results { display: block; }
        
        /* Progress bar */
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #f0f0f0;
            border-radius: 4px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            border-radius: 4px;
            transition: width 0.3s ease;
            width: 8.33%;
        }
        
        /* Update progress based on current question */
        #nav-q1:checked ~ .progress-container .progress-fill { width: 8.33%; }
        #nav-q2:checked ~ .progress-container .progress-fill { width: 16.66%; }
        #nav-q3:checked ~ .progress-container .progress-fill { width: 25%; }
        #nav-q4:checked ~ .progress-container .progress-fill { width: 33.33%; }
        #nav-q5:checked ~ .progress-container .progress-fill { width: 41.66%; }
        #nav-q6:checked ~ .progress-container .progress-fill { width: 50%; }
        #nav-q7:checked ~ .progress-container .progress-fill { width: 58.33%; }
        #nav-q8:checked ~ .progress-container .progress-fill { width: 66.66%; }
        #nav-q9:checked ~ .progress-container .progress-fill { width: 75%; }
        #nav-q10:checked ~ .progress-container .progress-fill { width: 83.33%; }
        #nav-q11:checked ~ .progress-container .progress-fill { width: 91.66%; }
        #nav-q12:checked ~ .progress-container .progress-fill { width: 100%; }
        #nav-results:checked ~ .progress-container .progress-fill { width: 100%; }
        
        .question-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 20px;
            line-height: 1.4;
        }
        
        .question-type {
            font-size: 0.9rem;
            color: #888;
            margin-bottom: 15px;
            font-style: italic;
        }
        
        .options-container {
            display: grid;
            gap: 12px;
            margin-bottom: 30px;
        }
        
        .option {
            position: relative;
        }
        
        .option input[type="checkbox"] {
            position: absolute;
            opacity: 0;
            cursor: pointer;
        }
        
        .option-label {
            display: flex;
            align-items: center;
            padding: 16px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            background: #fafafa;
            gap: 12px;
        }
        
        .option-label:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }
        
        .option input[type="checkbox"]:checked + .option-label {
            border-color: #667eea;
            background: #667eea;
            color: white;
        }
        
        .option-emoji {
            font-size: 1.5rem;
            flex-shrink: 0;
        }
        
        .option-text {
            font-size: 1rem;
            font-weight: 500;
            line-height: 1.4;
        }
        
        .navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
        }
        
        .nav-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .nav-btn.primary {
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .nav-btn.primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        
        .nav-btn.secondary {
            background: #f0f0f0;
            color: #666;
        }
        
        .nav-btn.secondary:hover {
            background: #e0e0e0;
        }
        
        .nav-btn:active {
            transform: translateY(0px) scale(0.98);
        }
        
        .nav-btn.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            pointer-events: none;
        }
        
        .question-counter {
            font-size: 0.9rem;
            color: #888;
        }
        
        /* Update question counter */
        #nav-q1:checked ~ .question-slide .question-counter::after { content: "Question 1 of 12"; }
        #nav-q2:checked ~ .question-slide .question-counter::after { content: "Question 2 of 12"; }
        #nav-q3:checked ~ .question-slide .question-counter::after { content: "Question 3 of 12"; }
        #nav-q4:checked ~ .question-slide .question-counter::after { content: "Question 4 of 12"; }
        #nav-q5:checked ~ .question-slide .question-counter::after { content: "Question 5 of 12"; }
        #nav-q6:checked ~ .question-slide .question-counter::after { content: "Question 6 of 12"; }
        #nav-q7:checked ~ .question-slide .question-counter::after { content: "Question 7 of 12"; }
        #nav-q8:checked ~ .question-slide .question-counter::after { content: "Question 8 of 12"; }
        #nav-q9:checked ~ .question-slide .question-counter::after { content: "Question 9 of 12"; }
        #nav-q10:checked ~ .question-slide .question-counter::after { content: "Question 10 of 12"; }
        #nav-q11:checked ~ .question-slide .question-counter::after { content: "Question 11 of 12"; }
        #nav-q12:checked ~ .question-slide .question-counter::after { content: "Question 12 of 12"; }
        #nav-results:checked ~ .question-slide .question-counter::after { content: "Results"; }
        
        /* Live preview section */
        .live-preview {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9ff;
            border-radius: 8px;
            border-left: 4px solid #667eea;
            display: none;
        }
        
        /* Show preview when selections are made */
        .quiz-container:has(input[type="checkbox"]:checked) .live-preview {
            display: block;
        }
        
        .preview-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 10px;
        }
        
        .preview-text {
            color: #666;
            line-height: 1.5;
            font-size: 0.95rem;
        }
        
        /* CSS-only results calculation using :has() selector */
        .result-section {
            display: none;
            margin: 20px 0;
            padding: 30px;
            border-radius: 12px;
            border-left: 4px solid #667eea;
            text-align: left;
        }

        /* Technical Director - show when multiple technical selections across questions */
        .quiz-container:has(input[data-type="technical"]:checked):has(#q5_technical:checked):has(#q9_technical:checked) .result-technical-director,
        .quiz-container:has(input[data-type="technical"]:checked):has(#q10_debugging:checked):has(#q12_technical:checked) .result-technical-director,
        .quiz-container:has(#q1_technical:checked):has(#q2_technical:checked):has(#q3_technical:checked) .result-technical-director {
            display: block;
        }

        /* Creative Director - show when multiple creative selections */
        .quiz-container:has(input[data-type="creative"]:checked):has(#q5_creative:checked):has(#q11_creative:checked) .result-creative-director,
        .quiz-container:has(input[data-type="creative"]:checked):has(#q12_creative:checked):has(#q9_creative:checked) .result-creative-director,
        .quiz-container:has(#q1_creative:checked):has(#q2_creative:checked):has(#q3_creative:checked) .result-creative-director {
            display: block;
        }

        /* Art Director - show when visual + leadership selections */
        .quiz-container:has(input[data-type="visual"]:checked):has(#q7_visual:checked):has(input[data-type="leadership"]:checked) .result-art-director,
        .quiz-container:has(#q1_visual:checked):has(#q4_visual:checked):has(#q12_creative:checked) .result-art-director,
        .quiz-container:has(input[data-type="visual"]:checked):has(input[data-type="creative"]:checked):has(#q10_presenting:checked) .result-art-director {
            display: block;
        }

        /* Show default result when no specific pattern is detected */
        .quiz-container:not(:has(.result-technical-director, .result-creative-director, .result-art-director)) .result-default {
            display: block;
        }

        /* Results styling */
        .results-container {
            text-align: center;
            padding: 40px 20px;
        }

        .results-title {
            font-size: 2.5rem;
            font-weight: bold;
            color: #1a1a1a;
            margin-bottom: 20px;
        }

        .results-role {
            font-size: 2rem;
            font-weight: bold;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 20px;
        }

        .results-description {
            font-size: 1.1rem;
            color: #666;
            line-height: 1.6;
            margin-bottom: 30px;
            text-align: left;
        }

        /* Book recommendation styles */
        .book-recommendation {
            background: #f8f9ff;
            border: 2px solid #667eea;
            border-radius: 12px;
            padding: 24px;
            margin: 20px 0;
            text-align: left;
        }

        .book-header {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            align-items: flex-start;
        }

        .book-cover {
            flex-shrink: 0;
            width: 80px;
        }

        .book-cover img {
            width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .book-info {
            flex: 1;
        }

        .book-title {
            margin: 0 0 8px 0;
            color: #1a1a1a;
            font-size: 1.3rem;
            font-weight: bold;
            line-height: 1.3;
        }

        .book-author {
            margin: 0 0 12px 0;
            color: #666;
            font-size: 1.1rem;
            font-style: italic;
        }

        .purchase-options {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
            margin: 15px 0;
        }

        .purchase-link {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 10px 16px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            font-size: 0.95rem;
            transition: all 0.2s ease;
        }

        .purchase-link:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
            color: white;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="quiz-container">
        <div class="quiz-header">
            <h1 class="quiz-title">🎮 2025 Game Dev Career Quiz</h1>
            <p class="quiz-subtitle">Discover how your mind naturally works and what type of game development thinking you have! No experience needed - just answer based on what interests you most. Choose 1-2 answers per question for the best results.</p>
        </div>
        
        <!-- Hidden navigation radio buttons -->
        <input type="radio" name="nav" id="nav-q1" class="nav-radio" checked>
        <input type="radio" name="nav" id="nav-q2" class="nav-radio">
        <input type="radio" name="nav" id="nav-q3" class="nav-radio">
        <input type="radio" name="nav" id="nav-q4" class="nav-radio">
        <input type="radio" name="nav" id="nav-q5" class="nav-radio">
        <input type="radio" name="nav" id="nav-q6" class="nav-radio">
        <input type="radio" name="nav" id="nav-q7" class="nav-radio">
        <input type="radio" name="nav" id="nav-q8" class="nav-radio">
        <input type="radio" name="nav" id="nav-q9" class="nav-radio">
        <input type="radio" name="nav" id="nav-q10" class="nav-radio">
        <input type="radio" name="nav" id="nav-q11" class="nav-radio">
        <input type="radio" name="nav" id="nav-q12" class="nav-radio">
        <input type="radio" name="nav" id="nav-results" class="nav-radio">
        
        <!-- Progress bar -->
        <div class="progress-container">
            <div class="progress-bar">
                <div class="progress-fill"></div>
            </div>
        </div>

        <!-- Question 1 -->
        <div class="question-slide q1">
            <div class="question-type">Choose up to 2 options</div>
            <h2 class="question-title">When you imagine starting a creative project, what excites you most?</h2>
            <div class="options-container">
                <div class="option">
                    <input type="checkbox" id="q1_creative" name="q1" data-type="creative">
                    <label for="q1_creative" class="option-label">
                        <span class="option-emoji">🎨</span>
                        <span class="option-text">Imagining the big picture and how everything will look and feel</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q1_technical" name="q1" data-type="technical">
                    <label for="q1_technical" class="option-label">
                        <span class="option-emoji">⚙️</span>
                        <span class="option-text">Figuring out how things work and solving complex puzzles</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q1_visual" name="q1" data-type="visual">
                    <label for="q1_visual" class="option-label">
                        <span class="option-emoji">🎭</span>
                        <span class="option-text">Creating beautiful visuals and artistic concepts</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q1_business" name="q1" data-type="business">
                    <label for="q1_business" class="option-label">
                        <span class="option-emoji">👥</span>
                        <span class="option-text">Understanding what people want and how they'll react</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q1_narrative" name="q1" data-type="narrative">
                    <label for="q1_narrative" class="option-label">
                        <span class="option-emoji">📖</span>
                        <span class="option-text">Crafting stories and emotional experiences</span>
                    </label>
                </div>
            </div>
            <div class="navigation">
                <span class="nav-btn secondary disabled">Previous</span>
                <span class="question-counter"></span>
                <label for="nav-q2" class="nav-btn primary">Next</label>
            </div>
        </div>

        <!-- Question 2 -->
        <div class="question-slide q2">
            <div class="question-type">Choose up to 2 options</div>
            <h2 class="question-title">How do you prefer to receive feedback?</h2>
            <div class="options-container">
                <div class="option">
                    <input type="checkbox" id="q2_technical" name="q2" data-type="technical">
                    <label for="q2_technical" class="option-label">
                        <span class="option-emoji">💻</span>
                        <span class="option-text">Technical peer review with specific implementation suggestions</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q2_creative" name="q2" data-type="creative">
                    <label for="q2_creative" class="option-label">
                        <span class="option-emoji">🎨</span>
                        <span class="option-text">Creative direction and artistic vision guidance</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q2_user" name="q2" data-type="user">
                    <label for="q2_user" class="option-label">
                        <span class="option-emoji">❤️</span>
                        <span class="option-text">User reactions and emotional responses to your work</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q2_business" name="q2" data-type="business">
                    <label for="q2_business" class="option-label">
                        <span class="option-emoji">📈</span>
                        <span class="option-text">Data-driven insights and performance metrics</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q2_innovation" name="q2" data-type="innovation">
                    <label for="q2_innovation" class="option-label">
                        <span class="option-emoji">🚀</span>
                        <span class="option-text">Recognition for innovation and pushing boundaries</span>
                    </label>
                </div>
            </div>
            <div class="navigation">
                <label for="nav-q1" class="nav-btn secondary">Previous</label>
                <span class="question-counter"></span>
                <label for="nav-q3" class="nav-btn primary">Next</label>
            </div>
        </div>

        <!-- Question 3 -->
        <div class="question-slide q3">
            <div class="question-type">Choose up to 2 options</div>
            <h2 class="question-title">When you encounter a challenging problem, what's your instinct?</h2>
            <div class="options-container">
                <div class="option">
                    <input type="checkbox" id="q3_technical" name="q3" data-type="technical">
                    <label for="q3_technical" class="option-label">
                        <span class="option-emoji">🔬</span>
                        <span class="option-text">Break it down into smaller, logical steps</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q3_creative" name="q3" data-type="creative">
                    <label for="q3_creative" class="option-label">
                        <span class="option-emoji">💡</span>
                        <span class="option-text">Try different creative approaches until something clicks</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q3_social" name="q3" data-type="social">
                    <label for="q3_social" class="option-label">
                        <span class="option-emoji">👥</span>
                        <span class="option-text">Talk it through with others to get different perspectives</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q3_research" name="q3" data-type="research">
                    <label for="q3_research" class="option-label">
                        <span class="option-emoji">📚</span>
                        <span class="option-text">Research how others have solved similar problems</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q3_goal" name="q3" data-type="goal">
                    <label for="q3_goal" class="option-label">
                        <span class="option-emoji">🎯</span>
                        <span class="option-text">Focus on the end goal and work backwards</span>
                    </label>
                </div>
            </div>
            <div class="navigation">
                <label for="nav-q2" class="nav-btn secondary">Previous</label>
                <span class="question-counter"></span>
                <label for="nav-q4" class="nav-btn primary">Next</label>
            </div>
        </div>

        <!-- Question 4 -->
        <div class="question-slide q4">
            <div class="question-type">Choose up to 2 options</div>
            <h2 class="question-title">How do you naturally like to share your ideas?</h2>
            <div class="options-container">
                <div class="option">
                    <input type="checkbox" id="q4_visual" name="q4" data-type="visual">
                    <label for="q4_visual" class="option-label">
                        <span class="option-emoji">🎨</span>
                        <span class="option-text">Show through visuals, sketches, or prototypes</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q4_documentation" name="q4" data-type="technical">
                    <label for="q4_documentation" class="option-label">
                        <span class="option-emoji">📝</span>
                        <span class="option-text">Write detailed explanations and documentation</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q4_presentation" name="q4" data-type="social">
                    <label for="q4_presentation" class="option-label">
                        <span class="option-emoji">🗣️</span>
                        <span class="option-text">Present and discuss in person or meetings</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q4_prototype" name="q4" data-type="creative">
                    <label for="q4_prototype" class="option-label">
                        <span class="option-emoji">🎮</span>
                        <span class="option-text">Build something people can actually try</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q4_data" name="q4" data-type="business">
                    <label for="q4_data" class="option-label">
                        <span class="option-emoji">📊</span>
                        <span class="option-text">Use data and examples to make the case</span>
                    </label>
                </div>
            </div>
            <div class="navigation">
                <label for="nav-q3" class="nav-btn secondary">Previous</label>
                <span class="question-counter"></span>
                <label for="nav-q5" class="nav-btn primary">Next</label>
            </div>
        </div>

        <!-- Question 5 -->
        <div class="question-slide q5">
            <div class="question-type">Choose up to 2 options</div>
            <h2 class="question-title">What motivates you most in your career?</h2>
            <div class="options-container">
                <div class="option">
                    <input type="checkbox" id="q5_technical" name="q5" data-type="technical">
                    <label for="q5_technical" class="option-label">
                        <span class="option-emoji">🧩</span>
                        <span class="option-text">Solving complex challenges that others can't</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q5_creative" name="q5" data-type="creative">
                    <label for="q5_creative" class="option-label">
                        <span class="option-emoji">🎨</span>
                        <span class="option-text">Bringing creative visions to life</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q5_impact" name="q5" data-type="user">
                    <label for="q5_impact" class="option-label">
                        <span class="option-emoji">❤️</span>
                        <span class="option-text">Creating experiences that emotionally impact people</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q5_scale" name="q5" data-type="business">
                    <label for="q5_scale" class="option-label">
                        <span class="option-emoji">📈</span>
                        <span class="option-text">Building products that reach millions</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q5_leadership" name="q5" data-type="leadership">
                    <label for="q5_leadership" class="option-label">
                        <span class="option-emoji">👑</span>
                        <span class="option-text">Leading teams and shaping company direction</span>
                    </label>
                </div>
            </div>
            <div class="navigation">
                <label for="nav-q4" class="nav-btn secondary">Previous</label>
                <span class="question-counter"></span>
                <label for="nav-q6" class="nav-btn primary">Next</label>
            </div>
        </div>

        <!-- Question 6 -->
        <div class="question-slide q6">
            <div class="question-type">Choose up to 2 options</div>
            <h2 class="question-title">How do you approach learning new skills?</h2>
            <div class="options-container">
                <div class="option">
                    <input type="checkbox" id="q6_technical" name="q6" data-type="technical">
                    <label for="q6_technical" class="option-label">
                        <span class="option-emoji">📚</span>
                        <span class="option-text">Deep study of documentation and technical resources</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q6_creative" name="q6" data-type="creative">
                    <label for="q6_creative" class="option-label">
                        <span class="option-emoji">🎨</span>
                        <span class="option-text">Hands-on experimentation and creative exploration</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q6_social" name="q6" data-type="social">
                    <label for="q6_social" class="option-label">
                        <span class="option-emoji">👨‍🏫</span>
                        <span class="option-text">Learning from mentors and peer collaboration</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q6_analysis" name="q6" data-type="business">
                    <label for="q6_analysis" class="option-label">
                        <span class="option-emoji">📊</span>
                        <span class="option-text">Analyzing case studies and industry examples</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q6_building" name="q6" data-type="creative">
                    <label for="q6_building" class="option-label">
                        <span class="option-emoji">🔨</span>
                        <span class="option-text">Building projects and learning through iteration</span>
                    </label>
                </div>
            </div>
            <div class="navigation">
                <label for="nav-q5" class="nav-btn secondary">Previous</label>
                <span class="question-counter"></span>
                <label for="nav-q7" class="nav-btn primary">Next</label>
            </div>
        </div>

        <!-- Question 7 -->
        <div class="question-slide q7">
            <div class="question-type">Choose up to 2 options</div>
            <h2 class="question-title">What games do you find yourself drawn to?</h2>
            <div class="options-container">
                <div class="option">
                    <input type="checkbox" id="q7_technical" name="q7" data-type="technical">
                    <label for="q7_technical" class="option-label">
                        <span class="option-emoji">🎮</span>
                        <span class="option-text">Technical showcases (realistic graphics, complex systems)</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q7_visual" name="q7" data-type="visual">
                    <label for="q7_visual" class="option-label">
                        <span class="option-emoji">🎨</span>
                        <span class="option-text">Artistic indies (unique visual style, creative gameplay)</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q7_narrative" name="q7" data-type="narrative">
                    <label for="q7_narrative" class="option-label">
                        <span class="option-emoji">📖</span>
                        <span class="option-text">Story-rich RPGs (deep narrative, character development)</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q7_audio" name="q7" data-type="audio">
                    <label for="q7_audio" class="option-label">
                        <span class="option-emoji">🎵</span>
                        <span class="option-text">Music/rhythm games (audio-focused, precision-based)</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q7_multiplayer" name="q7" data-type="social">
                    <label for="q7_multiplayer" class="option-label">
                        <span class="option-emoji">👥</span>
                        <span class="option-text">Multiplayer competitive (community, balance, esports)</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q7_casual" name="q7" data-type="business">
                    <label for="q7_casual" class="option-label">
                        <span class="option-emoji">📱</span>
                        <span class="option-text">Mobile/casual (accessible, broad appeal)</span>
                    </label>
                </div>
            </div>
            <div class="navigation">
                <label for="nav-q6" class="nav-btn secondary">Previous</label>
                <span class="question-counter"></span>
                <label for="nav-q8" class="nav-btn primary">Next</label>
            </div>
        </div>

        <!-- Question 8 -->
        <div class="question-slide q8">
            <div class="question-type">Choose up to 2 options</div>
            <h2 class="question-title">Choose your workflow preferences</h2>
            <div class="options-container">
                <div class="option">
                    <input type="checkbox" id="q8_agile" name="q8" data-type="technical">
                    <label for="q8_agile" class="option-label">
                        <span class="option-emoji">🏃‍♂️</span>
                        <span class="option-text">Sprint-based with quick iterations</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q8_creative" name="q8" data-type="creative">
                    <label for="q8_creative" class="option-label">
                        <span class="option-emoji">🎨</span>
                        <span class="option-text">Creative exploration with flexible timelines</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q8_structured" name="q8" data-type="business">
                    <label for="q8_structured" class="option-label">
                        <span class="option-emoji">📋</span>
                        <span class="option-text">Structured phases with clear documentation</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q8_feedback" name="q8" data-type="user">
                    <label for="q8_feedback" class="option-label">
                        <span class="option-emoji">🔄</span>
                        <span class="option-text">Continuous feedback and user testing cycles</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q8_prototyping" name="q8" data-type="innovation">
                    <label for="q8_prototyping" class="option-label">
                        <span class="option-emoji">🚀</span>
                        <span class="option-text">Rapid prototyping and experimentation</span>
                    </label>
                </div>
            </div>
            <div class="navigation">
                <label for="nav-q7" class="nav-btn secondary">Previous</label>
                <span class="question-counter"></span>
                <label for="nav-q9" class="nav-btn primary">Next</label>
            </div>
        </div>

        <!-- Question 9 -->
        <div class="question-slide q9">
            <div class="question-type">Choose up to 2 options</div>
            <h2 class="question-title">What aspect of game development do you find most rewarding?</h2>
            <div class="options-container">
                <div class="option">
                    <input type="checkbox" id="q9_technical" name="q9" data-type="technical">
                    <label for="q9_technical" class="option-label">
                        <span class="option-emoji">⚡</span>
                        <span class="option-text">Solving complex technical challenges that seemed impossible</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q9_creative" name="q9" data-type="creative">
                    <label for="q9_creative" class="option-label">
                        <span class="option-emoji">🎨</span>
                        <span class="option-text">Seeing your creative vision come to life</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q9_user" name="q9" data-type="user">
                    <label for="q9_user" class="option-label">
                        <span class="option-emoji">😊</span>
                        <span class="option-text">Watching players enjoy and connect with your work</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q9_shipping" name="q9" data-type="business">
                    <label for="q9_shipping" class="option-label">
                        <span class="option-emoji">🚀</span>
                        <span class="option-text">Shipping a polished product that reaches many people</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q9_mentoring" name="q9" data-type="leadership">
                    <label for="q9_mentoring" class="option-label">
                        <span class="option-emoji">🌱</span>
                        <span class="option-text">Mentoring others and building great teams</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q9_innovation" name="q9" data-type="innovation">
                    <label for="q9_innovation" class="option-label">
                        <span class="option-emoji">🔬</span>
                        <span class="option-text">Pushing the boundaries of what's technically possible</span>
                    </label>
                </div>
            </div>
            <div class="navigation">
                <label for="nav-q8" class="nav-btn secondary">Previous</label>
                <span class="question-counter"></span>
                <label for="nav-q10" class="nav-btn primary">Next</label>
            </div>
        </div>

        <!-- Question 10 -->
        <div class="question-slide q10">
            <div class="question-type">Rate your interest - Check the ones you enjoy</div>
            <h2 class="question-title">How much do you enjoy these activities?</h2>
            <div class="options-container">
                <div class="option">
                    <input type="checkbox" id="q10_management" name="q10" data-type="leadership">
                    <label for="q10_management" class="option-label">
                        <span class="option-emoji">👥</span>
                        <span class="option-text">Managing people and their career development</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q10_pressure" name="q10" data-type="business">
                    <label for="q10_pressure" class="option-label">
                        <span class="option-emoji">⏰</span>
                        <span class="option-text">Working under tight deadlines and pressure</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q10_presenting" name="q10" data-type="creative">
                    <label for="q10_presenting" class="option-label">
                        <span class="option-emoji">🎨</span>
                        <span class="option-text">Presenting creative work for critique and feedback</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q10_debugging" name="q10" data-type="technical">
                    <label for="q10_debugging" class="option-label">
                        <span class="option-emoji">🔧</span>
                        <span class="option-text">Debugging complex technical issues</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q10_stakeholders" name="q10" data-type="business">
                    <label for="q10_stakeholders" class="option-label">
                        <span class="option-emoji">💬</span>
                        <span class="option-text">Communicating with external stakeholders</span>
                    </label>
                </div>
            </div>
            <div class="navigation">
                <label for="nav-q9" class="nav-btn secondary">Previous</label>
                <span class="question-counter"></span>
                <label for="nav-q11" class="nav-btn primary">Next</label>
            </div>
        </div>

        <!-- Question 11 -->
        <div class="question-slide q11">
            <div class="question-type">Rank your top 2 career motivators - Check your top 2</div>
            <h2 class="question-title">What drives you most in your career?</h2>
            <div class="options-container">
                <div class="option">
                    <input type="checkbox" id="q11_challenges" name="q11" data-type="technical">
                    <label for="q11_challenges" class="option-label">
                        <span class="option-emoji">🧩</span>
                        <span class="option-text">Solving complex challenges that others can't</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q11_creative" name="q11" data-type="creative">
                    <label for="q11_creative" class="option-label">
                        <span class="option-emoji">🎨</span>
                        <span class="option-text">Bringing creative visions to life</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q11_impact" name="q11" data-type="user">
                    <label for="q11_impact" class="option-label">
                        <span class="option-emoji">❤️</span>
                        <span class="option-text">Creating experiences that emotionally impact people</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q11_scale" name="q11" data-type="business">
                    <label for="q11_scale" class="option-label">
                        <span class="option-emoji">📈</span>
                        <span class="option-text">Building products that reach millions</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q11_leadership" name="q11" data-type="leadership">
                    <label for="q11_leadership" class="option-label">
                        <span class="option-emoji">👑</span>
                        <span class="option-text">Leading teams and shaping company direction</span>
                    </label>
                </div>
            </div>
            <div class="navigation">
                <label for="nav-q10" class="nav-btn secondary">Previous</label>
                <span class="question-counter"></span>
                <label for="nav-q12" class="nav-btn primary">Next</label>
            </div>
        </div>

        <!-- Question 12 -->
        <div class="question-slide q12">
            <div class="question-type">Final priority check - Check your top 3 most important</div>
            <h2 class="question-title">What matters most to you in your ideal role?</h2>
            <div class="options-container">
                <div class="option">
                    <input type="checkbox" id="q12_creative" name="q12" data-type="creative">
                    <label for="q12_creative" class="option-label">
                        <span class="option-emoji">🎯</span>
                        <span class="option-text">Creative freedom and artistic expression</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q12_financial" name="q12" data-type="business">
                    <label for="q12_financial" class="option-label">
                        <span class="option-emoji">💰</span>
                        <span class="option-text">Financial success and career growth</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q12_technical" name="q12" data-type="technical">
                    <label for="q12_technical" class="option-label">
                        <span class="option-emoji">🔧</span>
                        <span class="option-text">Technical excellence and innovation</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q12_meaningful" name="q12" data-type="user">
                    <label for="q12_meaningful" class="option-label">
                        <span class="option-emoji">❤️</span>
                        <span class="option-text">Meaningful impact on players</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q12_collaborative" name="q12" data-type="social">
                    <label for="q12_collaborative" class="option-label">
                        <span class="option-emoji">🤝</span>
                        <span class="option-text">Collaborative team environment</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q12_leadership" name="q12" data-type="leadership">
                    <label for="q12_leadership" class="option-label">
                        <span class="option-emoji">👑</span>
                        <span class="option-text">Leadership and decision-making authority</span>
                    </label>
                </div>
            </div>
            <div class="navigation">
                <label for="nav-q11" class="nav-btn secondary">Previous</label>
                <span class="question-counter"></span>
                <label for="nav-results" class="nav-btn primary">See Results</label>
            </div>
        </div>

        <!-- Results Section -->
        <div class="question-slide results">
            <div class="results-container">
                <h2 class="results-title">🎉 Your Game Dev Career Results</h2>

                <!-- Technical Director Result -->
                <div class="result-section result-technical-director">
                    <div class="results-role">You think like a Technical Director!</div>
                    <div class="results-description">
                        <p>You're the technical leader who makes impossible things possible. Like the technical directors behind games like The Last of Us Part II, Red Dead Redemption 2, or Cyberpunk 2077, you solve the hardest problems and guide teams through complex technical challenges. You see the big picture of how systems work together and can architect solutions that scale.</p>
                    </div>

                    <div class="book-recommendation">
                        <h3>📚 Recommended Reading:</h3>
                        <div class="book-header">
                            <div class="book-cover">
                                <img src="https://images-na.ssl-images-amazon.com/images/P/0201835959.01.L.jpg" alt="The Mythical Man-Month" loading="lazy">
                            </div>
                            <div class="book-info">
                                <h4 class="book-title">"The Mythical Man-Month"</h4>
                                <p class="book-author">by Frederick Brooks</p>
                                <div style="display: flex; gap: 16px; flex-wrap: wrap; font-size: 0.9rem; color: #888; margin: 8px 0;">
                                    <span style="background: #e8f0fe; padding: 4px 8px; border-radius: 4px; font-weight: 500;">8-12 hours</span>
                                    <span style="background: #e8f0fe; padding: 4px 8px; border-radius: 4px; font-weight: 500;">Intermediate</span>
                                </div>
                            </div>
                        </div>

                        <div style="margin: 15px 0;">
                            <p><strong>Why this book is essential:</strong> The definitive guide to managing software projects and technical teams. Brooks' insights on communication, team scaling, and project management are as relevant today as when first published. Essential for anyone moving into technical leadership.</p>
                        </div>

                        <div class="purchase-options">
                            <a href="https://amzn.to/408cgnc" target="_blank" class="purchase-link">📚 Hardcover</a>
                            <a href="https://amzn.to/44KJFWr" target="_blank" class="purchase-link">📱 Kindle</a>
                            <a href="https://amzn.to/44S7cpw" target="_blank" class="purchase-link">🎧 Audiobook</a>
                        </div>
                    </div>

                    <div style="margin: 30px 0; padding: 20px; background: #fff8f0; border-radius: 8px; border-left: 4px solid #ff9800;">
                        <h3 style="margin: 0 0 15px 0; color: #1a1a1a; font-size: 1.2rem;">🚀 Your First 30 Days:</h3>
                        <ol style="margin: 0; padding-left: 20px; color: #444; line-height: 1.6;">
                            <li><strong>Week 1:</strong> Set up a technical blog and write about a complex problem you've solved</li>
                            <li><strong>Week 2:</strong> Start a side project that demonstrates system architecture skills</li>
                            <li><strong>Week 3:</strong> Join technical communities (Reddit r/gamedev, Discord servers)</li>
                            <li><strong>Week 4:</strong> Begin contributing to open-source game engines or tools</li>
                        </ol>
                    </div>
                </div>

                <!-- Creative Director Result -->
                <div class="result-section result-creative-director">
                    <div class="results-role">You think like a Creative Director!</div>
                    <div class="results-description">
                        <p>You're the creative mastermind who ensures all elements serve a unified vision. Think of directors like Amy Hennig (Uncharted), Ken Levine (BioShock), or Hideo Kojima (Metal Gear). You see how gameplay, story, art, and sound weave together to create experiences that players remember for years. You understand that great games aren't just fun - they're meaningful.</p>
                    </div>

                    <div class="book-recommendation">
                        <h3>📚 Recommended Reading:</h3>
                        <div class="book-header">
                            <div class="book-cover">
                                <img src="https://images-na.ssl-images-amazon.com/images/P/0881341177.01.L.jpg" alt="The Art of Computer Game Design" loading="lazy">
                            </div>
                            <div class="book-info">
                                <h4 class="book-title">"The Art of Computer Game Design"</h4>
                                <p class="book-author">by Chris Crawford</p>
                                <div style="display: flex; gap: 16px; flex-wrap: wrap; font-size: 0.9rem; color: #888; margin: 8px 0;">
                                    <span style="background: #e8f0fe; padding: 4px 8px; border-radius: 4px; font-weight: 500;">3-5 hours</span>
                                    <span style="background: #e8f0fe; padding: 4px 8px; border-radius: 4px; font-weight: 500;">Beginner</span>
                                </div>
                            </div>
                        </div>

                        <div style="margin: 15px 0;">
                            <p><strong>Why this book is essential:</strong> The foundational text on game design philosophy. Crawford's insights into what makes games engaging and meaningful remain relevant decades later. Essential reading for understanding the art and craft of game design.</p>
                        </div>

                        <div class="purchase-options">
                            <a href="https://amzn.to/4krS071" target="_blank" class="purchase-link">📖 Paperback</a>
                            <a href="https://amzn.to/4nKw2PO" target="_blank" class="purchase-link">📱 Kindle</a>
                            <a href="https://amzn.to/44CnNwf" target="_blank" class="purchase-link">📚 Hardcover</a>
                        </div>

                        <div style="margin-top: 15px; padding: 10px; background: #e8f5e8; border-radius: 6px; font-size: 0.9rem; color: #2e7d32;">
                            💡 <strong>Free Option:</strong> Available free online at Chris Crawford's website
                        </div>
                    </div>

                    <div style="margin: 30px 0; padding: 20px; background: #f8f9ff; border-radius: 8px; border-left: 4px solid #667eea;">
                        <h3 style="margin: 0 0 15px 0; color: #1a1a1a; font-size: 1.2rem;">🚀 Your First 30 Days:</h3>
                        <ol style="margin: 0; padding-left: 20px; color: #444; line-height: 1.6;">
                            <li><strong>Week 1:</strong> Create a game design document for an original concept</li>
                            <li><strong>Week 2:</strong> Build a simple prototype using Twine, Bitsy, or similar tools</li>
                            <li><strong>Week 3:</strong> Start a design blog analyzing games you love</li>
                            <li><strong>Week 4:</strong> Join game design communities and share your work</li>
                        </ol>
                    </div>
                </div>

                <!-- Art Director Result -->
                <div class="result-section result-art-director">
                    <div class="results-role">You think like an Art Director!</div>
                    <div class="results-description">
                        <p>You're the visual architect who defines how games look and feel. Like the art directors behind Journey, Ori and the Blind Forest, or Cuphead, you shape entire aesthetic experiences. You understand that art isn't just decoration - it's communication, emotion, and world-building all rolled into one. You see how color, composition, and style create meaning.</p>
                    </div>

                    <div class="book-recommendation">
                        <h3>📚 Recommended Reading:</h3>
                        <div class="book-header">
                            <div class="book-cover">
                                <img src="https://images-na.ssl-images-amazon.com/images/P/113801415X.01.L.jpg" alt="The Visual Story" loading="lazy">
                            </div>
                            <div class="book-info">
                                <h4 class="book-title">"The Visual Story"</h4>
                                <p class="book-author">by Bruce Block</p>
                                <div style="display: flex; gap: 16px; flex-wrap: wrap; font-size: 0.9rem; color: #888; margin: 8px 0;">
                                    <span style="background: #e8f0fe; padding: 4px 8px; border-radius: 4px; font-weight: 500;">8-12 hours</span>
                                    <span style="background: #e8f0fe; padding: 4px 8px; border-radius: 4px; font-weight: 500;">Intermediate</span>
                                </div>
                            </div>
                        </div>

                        <div style="margin: 15px 0;">
                            <p><strong>Why this book is essential:</strong> Essential for understanding visual storytelling and composition. While focused on film, the principles apply directly to game cinematics, level design, and visual communication. Perfect for art directors and visual designers.</p>
                        </div>

                        <div class="purchase-options">
                            <a href="https://amzn.to/4eH8LtF" target="_blank" class="purchase-link">📖 Paperback</a>
                            <a href="https://amzn.to/3TwrcYA" target="_blank" class="purchase-link">💻 eTextbook</a>
                            <a href="https://amzn.to/4lL7UdU" target="_blank" class="purchase-link">📚 Hardcover</a>
                        </div>
                    </div>

                    <div style="margin: 30px 0; padding: 20px; background: #f0fff8; border-radius: 8px; border-left: 4px solid #4caf50;">
                        <h3 style="margin: 0 0 15px 0; color: #1a1a1a; font-size: 1.2rem;">🚀 Your First 30 Days:</h3>
                        <ol style="margin: 0; padding-left: 20px; color: #444; line-height: 1.6;">
                            <li><strong>Week 1:</strong> Create mood boards for 3 different game genres</li>
                            <li><strong>Week 2:</strong> Design a character or environment concept sheet</li>
                            <li><strong>Week 3:</strong> Study and recreate lighting from your favorite games</li>
                            <li><strong>Week 4:</strong> Build an art portfolio website showcasing your game art</li>
                        </ol>
                    </div>
                </div>

                <!-- Default Game Designer Result -->
                <div class="result-section result-default">
                    <div class="results-role">You think like a Game Designer!</div>
                    <div class="results-description">
                        <p>You're the architect of fun - the person who crafts the rules, systems, and interactions that make games engaging. Whether designing levels like those in Portal, mechanics like those in Tetris, or progression systems like those in RPGs, you understand what makes players tick. You see the invisible systems that create joy, challenge, and meaning in interactive experiences.</p>
                    </div>

                    <div class="book-recommendation">
                        <h3>📚 Recommended Reading:</h3>
                        <div class="book-header">
                            <div class="book-cover">
                                <img src="https://images-na.ssl-images-amazon.com/images/P/0881341177.01.L.jpg" alt="The Art of Computer Game Design" loading="lazy">
                            </div>
                            <div class="book-info">
                                <h4 class="book-title">"The Art of Computer Game Design"</h4>
                                <p class="book-author">by Chris Crawford</p>
                                <div style="display: flex; gap: 16px; flex-wrap: wrap; font-size: 0.9rem; color: #888; margin: 8px 0;">
                                    <span style="background: #e8f0fe; padding: 4px 8px; border-radius: 4px; font-weight: 500;">3-5 hours</span>
                                    <span style="background: #e8f0fe; padding: 4px 8px; border-radius: 4px; font-weight: 500;">Beginner</span>
                                </div>
                            </div>
                        </div>

                        <div style="margin: 15px 0;">
                            <p><strong>Why this book is essential:</strong> The foundational text on game design philosophy. Crawford's insights into what makes games engaging and meaningful remain relevant decades later. Essential reading for understanding the art and craft of game design.</p>
                        </div>

                        <div class="purchase-options">
                            <a href="https://amzn.to/4krS071" target="_blank" class="purchase-link">📖 Paperback</a>
                            <a href="https://amzn.to/4nKw2PO" target="_blank" class="purchase-link">📱 Kindle</a>
                            <a href="https://amzn.to/44CnNwf" target="_blank" class="purchase-link">📚 Hardcover</a>
                        </div>

                        <div style="margin-top: 15px; padding: 10px; background: #e8f5e8; border-radius: 6px; font-size: 0.9rem; color: #2e7d32;">
                            💡 <strong>Free Option:</strong> Available free online at Chris Crawford's website
                        </div>
                    </div>

                    <div style="margin: 30px 0; padding: 20px; background: #f8f9ff; border-radius: 8px; border-left: 4px solid #667eea;">
                        <h3 style="margin: 0 0 15px 0; color: #1a1a1a; font-size: 1.2rem;">🚀 Your First 30 Days:</h3>
                        <ol style="margin: 0; padding-left: 20px; color: #444; line-height: 1.6;">
                            <li><strong>Week 1:</strong> Create a game design document for an original concept</li>
                            <li><strong>Week 2:</strong> Build a simple prototype using Twine, Bitsy, or similar tools</li>
                            <li><strong>Week 3:</strong> Start a design blog analyzing games you love</li>
                            <li><strong>Week 4:</strong> Join game design communities and share your work</li>
                        </ol>
                    </div>
                </div>

                <div style="margin-top: 30px; padding: 20px; background: #e8f5e8; border-radius: 8px; border-left: 4px solid #4caf50; text-align: left;">
                    <p style="margin: 0; color: #2e7d32; line-height: 1.6;">
                        🌟 <strong>Remember:</strong> The game industry values diverse perspectives and creative thinking. Your unique combination of interests and natural inclinations is exactly what makes great games possible. Start building, start creating, and don't be afraid to share your ideas with the world!
                    </p>
                </div>

                <div style="margin-top: 15px; font-size: 0.85rem; color: #666; font-style: italic; text-align: center;">
                    📚 As an Amazon Associate, I earn from qualifying purchases. This helps support the creation of more career resources like this quiz!
                </div>

                <div class="navigation">
                    <label for="nav-q1" class="nav-btn primary">Take Quiz Again</label>
                </div>
            </div>
        </div>

        <!-- Live Preview Section -->
        <div class="live-preview">
            <h3 class="preview-title">🎯 Preview: Based on your selections so far...</h3>
            <p class="preview-text">You're showing strong interest in creative and strategic thinking. This suggests you might think like a <strong>Creative Director</strong> or <strong>Game Designer</strong>!</p>
            <p class="preview-text"><em>Complete all questions for your full personalized results and book recommendation.</em></p>
        </div>
    </div>
</body>
</html>
