<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-Frame-Options" content="ALLOWALL">
    <title>CSS-Only Quiz Navigation Demo</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            width: 100%;
            margin: 0;
            padding: 20px;
            background: #ffffff;
            box-sizing: border-box;
        }
        
        .quiz-container {
            width: 100%;
            max-width: none;
            padding: 30px;
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            box-sizing: border-box;
        }
        
        /* Hide the navigation radio buttons */
        .nav-radio {
            position: absolute;
            opacity: 0;
            pointer-events: none;
        }
        
        /* Question containers - hidden by default */
        .question-slide {
            display: none;
            animation: fadeIn 0.3s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateX(20px); }
            to { opacity: 1; transform: translateX(0); }
        }
        
        /* Show question 1 when radio 1 is checked */
        #nav-q1:checked ~ .question-slide.q1 {
            display: block;
        }
        
        /* Show question 2 when radio 2 is checked */
        #nav-q2:checked ~ .question-slide.q2 {
            display: block;
        }
        
        /* Show question 3 when radio 3 is checked */
        #nav-q3:checked ~ .question-slide.q3 {
            display: block;
        }
        
        /* Show results when results radio is checked */
        #nav-results:checked ~ .question-slide.results {
            display: block;
        }
        
        /* Progress bar styling */
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #f0f0f0;
            border-radius: 4px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            border-radius: 4px;
            transition: width 0.3s ease;
            width: 33%;
        }
        
        /* Update progress based on current question */
        #nav-q1:checked ~ .progress-container .progress-fill { width: 33%; }
        #nav-q2:checked ~ .progress-container .progress-fill { width: 66%; }
        #nav-q3:checked ~ .progress-container .progress-fill { width: 100%; }
        #nav-results:checked ~ .progress-container .progress-fill { width: 100%; }
        
        /* Question styling */
        .question-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 20px;
        }
        
        .option {
            position: relative;
            margin: 10px 0;
        }
        
        .option input[type="checkbox"] {
            position: absolute;
            opacity: 0;
        }
        
        .option-label {
            display: flex;
            align-items: center;
            padding: 16px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            background: #fafafa;
            gap: 12px;
        }
        
        .option-label:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }
        
        .option input[type="checkbox"]:checked + .option-label {
            border-color: #667eea;
            background: #667eea;
            color: white;
        }
        
        /* Navigation buttons */
        .nav-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 10px;
        }
        
        .nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        
        .nav-btn:active {
            transform: translateY(0px) scale(0.98);
        }
        
        .nav-btn.secondary {
            background: #f0f0f0;
            color: #666;
        }
        
        .nav-btn.secondary:hover {
            background: #e0e0e0;
        }
        
        .navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
        }
        
        .question-counter {
            font-size: 0.9rem;
            color: #888;
        }
        
        /* Update question counter based on current question */
        #nav-q1:checked ~ .question-slide .question-counter::after { content: "Question 1 of 3"; }
        #nav-q2:checked ~ .question-slide .question-counter::after { content: "Question 2 of 3"; }
        #nav-q3:checked ~ .question-slide .question-counter::after { content: "Question 3 of 3"; }
        #nav-results:checked ~ .question-slide .question-counter::after { content: "Results"; }
        
        /* Results styling */
        .results-container {
            text-align: center;
            padding: 40px 20px;
        }
        
        .results-title {
            font-size: 2.5rem;
            font-weight: bold;
            color: #1a1a1a;
            margin-bottom: 20px;
        }
        
        .results-role {
            font-size: 2rem;
            font-weight: bold;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="quiz-container">
        <h1 style="text-align: center; margin-bottom: 30px;">🎮 CSS-Only Quiz Navigation Demo</h1>
        
        <!-- Hidden radio buttons for navigation -->
        <input type="radio" name="nav" id="nav-q1" class="nav-radio" checked>
        <input type="radio" name="nav" id="nav-q2" class="nav-radio">
        <input type="radio" name="nav" id="nav-q3" class="nav-radio">
        <input type="radio" name="nav" id="nav-results" class="nav-radio">
        
        <!-- Progress bar -->
        <div class="progress-container">
            <div class="progress-bar">
                <div class="progress-fill"></div>
            </div>
        </div>
        
        <!-- Question 1 -->
        <div class="question-slide q1">
            <h2 class="question-title">What energizes you most when starting a new project?</h2>
            
            <div class="option">
                <input type="checkbox" id="q1_opt1">
                <label for="q1_opt1" class="option-label">
                    <span>🎨</span>
                    <span>Sketching out the big picture and overall vision</span>
                </label>
            </div>
            
            <div class="option">
                <input type="checkbox" id="q1_opt2">
                <label for="q1_opt2" class="option-label">
                    <span>⚙️</span>
                    <span>Diving deep into technical architecture</span>
                </label>
            </div>
            
            <div class="option">
                <input type="checkbox" id="q1_opt3">
                <label for="q1_opt3" class="option-label">
                    <span>🎭</span>
                    <span>Creating visual concepts and mood boards</span>
                </label>
            </div>
            
            <div class="navigation">
                <button class="nav-btn secondary" disabled>Previous</button>
                <span class="question-counter"></span>
                <label for="nav-q2" class="nav-btn">Next Question</label>
            </div>
        </div>
        
        <!-- Question 2 -->
        <div class="question-slide q2">
            <h2 class="question-title">How do you prefer to receive feedback?</h2>
            
            <div class="option">
                <input type="checkbox" id="q2_opt1">
                <label for="q2_opt1" class="option-label">
                    <span>💻</span>
                    <span>Technical peer review with specific suggestions</span>
                </label>
            </div>
            
            <div class="option">
                <input type="checkbox" id="q2_opt2">
                <label for="q2_opt2" class="option-label">
                    <span>🎨</span>
                    <span>Creative direction and artistic vision guidance</span>
                </label>
            </div>
            
            <div class="option">
                <input type="checkbox" id="q2_opt3">
                <label for="q2_opt3" class="option-label">
                    <span>❤️</span>
                    <span>User reactions and emotional responses</span>
                </label>
            </div>
            
            <div class="navigation">
                <label for="nav-q1" class="nav-btn secondary">Previous</label>
                <span class="question-counter"></span>
                <label for="nav-q3" class="nav-btn">Next Question</label>
            </div>
        </div>
        
        <!-- Question 3 -->
        <div class="question-slide q3">
            <h2 class="question-title">What motivates you most in your career?</h2>
            
            <div class="option">
                <input type="checkbox" id="q3_opt1">
                <label for="q3_opt1" class="option-label">
                    <span>🧩</span>
                    <span>Solving complex challenges that others can't</span>
                </label>
            </div>
            
            <div class="option">
                <input type="checkbox" id="q3_opt2">
                <label for="q3_opt2" class="option-label">
                    <span>🎨</span>
                    <span>Bringing creative visions to life</span>
                </label>
            </div>
            
            <div class="option">
                <input type="checkbox" id="q3_opt3">
                <label for="q3_opt3" class="option-label">
                    <span>❤️</span>
                    <span>Creating experiences that impact people</span>
                </label>
            </div>
            
            <div class="navigation">
                <label for="nav-q2" class="nav-btn secondary">Previous</label>
                <span class="question-counter"></span>
                <label for="nav-results" class="nav-btn">See Results</label>
            </div>
        </div>
        
        <!-- Results -->
        <div class="question-slide results">
            <div class="results-container">
                <h2 class="results-title">🎉 Your Results</h2>
                <div class="results-role">You think like a Creative Director!</div>
                <p>Based on your selections, you have a natural inclination toward big-picture thinking, creative vision, and bringing artistic concepts to life.</p>
                
                <div style="margin-top: 30px;">
                    <label for="nav-q1" class="nav-btn">Take Quiz Again</label>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
