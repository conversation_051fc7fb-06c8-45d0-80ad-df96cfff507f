<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-Frame-Options" content="ALLOWALL">
    <title>🎮 2025 Game Dev Career Quiz</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            width: 100%;
            margin: 0;
            padding: 20px;
            background: #ffffff;
            box-sizing: border-box;
        }
        
        .quiz-container {
            width: 100%;
            max-width: none;
            padding: 30px;
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            box-sizing: border-box;
        }
        
        .quiz-title {
            font-size: 2.5rem;
            font-weight: bold;
            color: #1a1a1a;
            margin-bottom: 10px;
            text-align: center;
        }
        
        .quiz-subtitle {
            font-size: 1.1rem;
            color: #666;
            line-height: 1.5;
            text-align: center;
            margin-bottom: 30px;
        }
        
        /* Hide navigation radio buttons */
        .nav-radio {
            position: absolute;
            opacity: 0;
            pointer-events: none;
        }
        
        /* Question slides - hidden by default */
        .question-slide {
            display: none;
            animation: fadeIn 0.3s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateX(20px); }
            to { opacity: 1; transform: translateX(0); }
        }
        
        /* Show specific questions when corresponding radio is checked */
        #nav-q1:checked ~ .question-slide.q1 { display: block; }
        #nav-q2:checked ~ .question-slide.q2 { display: block; }
        #nav-q3:checked ~ .question-slide.q3 { display: block; }
        #nav-q4:checked ~ .question-slide.q4 { display: block; }
        #nav-q5:checked ~ .question-slide.q5 { display: block; }
        #nav-q6:checked ~ .question-slide.q6 { display: block; }
        #nav-q7:checked ~ .question-slide.q7 { display: block; }
        #nav-q8:checked ~ .question-slide.q8 { display: block; }
        #nav-q9:checked ~ .question-slide.q9 { display: block; }
        #nav-q10:checked ~ .question-slide.q10 { display: block; }
        #nav-q11:checked ~ .question-slide.q11 { display: block; }
        #nav-q12:checked ~ .question-slide.q12 { display: block; }
        #nav-results:checked ~ .question-slide.results { display: block; }
        
        /* Progress bar */
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #f0f0f0;
            border-radius: 4px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            border-radius: 4px;
            transition: width 0.3s ease;
            width: 8.33%;
        }
        
        /* Update progress based on current question */
        #nav-q1:checked ~ .progress-container .progress-fill { width: 8.33%; }
        #nav-q2:checked ~ .progress-container .progress-fill { width: 16.66%; }
        #nav-q3:checked ~ .progress-container .progress-fill { width: 25%; }
        #nav-q4:checked ~ .progress-container .progress-fill { width: 33.33%; }
        #nav-q5:checked ~ .progress-container .progress-fill { width: 41.66%; }
        #nav-q6:checked ~ .progress-container .progress-fill { width: 50%; }
        #nav-q7:checked ~ .progress-container .progress-fill { width: 58.33%; }
        #nav-q8:checked ~ .progress-container .progress-fill { width: 66.66%; }
        #nav-q9:checked ~ .progress-container .progress-fill { width: 75%; }
        #nav-q10:checked ~ .progress-container .progress-fill { width: 83.33%; }
        #nav-q11:checked ~ .progress-container .progress-fill { width: 91.66%; }
        #nav-q12:checked ~ .progress-container .progress-fill { width: 100%; }
        #nav-results:checked ~ .progress-container .progress-fill { width: 100%; }
        
        .question-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 20px;
            line-height: 1.4;
        }
        
        .question-type {
            font-size: 0.9rem;
            color: #888;
            margin-bottom: 15px;
            font-style: italic;
        }
        
        .options-container {
            display: grid;
            gap: 12px;
            margin-bottom: 30px;
        }
        
        .option {
            position: relative;
        }
        
        .option input[type="checkbox"] {
            position: absolute;
            opacity: 0;
            cursor: pointer;
        }
        
        .option-label {
            display: flex;
            align-items: center;
            padding: 16px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            background: #fafafa;
            gap: 12px;
        }
        
        .option-label:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }
        
        .option input[type="checkbox"]:checked + .option-label {
            border-color: #667eea;
            background: #667eea;
            color: white;
        }
        
        .option-emoji {
            font-size: 1.5rem;
            flex-shrink: 0;
        }
        
        .option-text {
            font-size: 1rem;
            font-weight: 500;
            line-height: 1.4;
        }
        
        .navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
        }
        
        .nav-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .nav-btn.primary {
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .nav-btn.primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        
        .nav-btn.secondary {
            background: #f0f0f0;
            color: #666;
        }
        
        .nav-btn.secondary:hover {
            background: #e0e0e0;
        }
        
        .nav-btn:active {
            transform: translateY(0px) scale(0.98);
        }
        
        .nav-btn:disabled,
        .nav-btn.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            pointer-events: none;
        }
        
        .question-counter {
            font-size: 0.9rem;
            color: #888;
        }
        
        /* Update question counter based on current question */
        #nav-q1:checked ~ .question-slide .question-counter::after { content: "Question 1 of 12"; }
        #nav-q2:checked ~ .question-slide .question-counter::after { content: "Question 2 of 12"; }
        #nav-q3:checked ~ .question-slide .question-counter::after { content: "Question 3 of 12"; }
        #nav-q4:checked ~ .question-slide .question-counter::after { content: "Question 4 of 12"; }
        #nav-q5:checked ~ .question-slide .question-counter::after { content: "Question 5 of 12"; }
        #nav-q6:checked ~ .question-slide .question-counter::after { content: "Question 6 of 12"; }
        #nav-q7:checked ~ .question-slide .question-counter::after { content: "Question 7 of 12"; }
        #nav-q8:checked ~ .question-slide .question-counter::after { content: "Question 8 of 12"; }
        #nav-q9:checked ~ .question-slide .question-counter::after { content: "Question 9 of 12"; }
        #nav-q10:checked ~ .question-slide .question-counter::after { content: "Question 10 of 12"; }
        #nav-q11:checked ~ .question-slide .question-counter::after { content: "Question 11 of 12"; }
        #nav-q12:checked ~ .question-slide .question-counter::after { content: "Question 12 of 12"; }
        #nav-results:checked ~ .question-slide .question-counter::after { content: "Results"; }
        
        /* Live preview section */
        .live-preview {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9ff;
            border-radius: 8px;
            border-left: 4px solid #667eea;
            display: none;
        }
        
        /* Show preview when selections are made */
        .quiz-container:has(input[type="checkbox"]:checked) .live-preview {
            display: block;
        }
        
        .preview-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 10px;
        }
        
        .preview-text {
            color: #666;
            line-height: 1.5;
            font-size: 0.95rem;
        }
    </style>
</head>
<body>
    <div class="quiz-container">
        <div class="quiz-header">
            <h1 class="quiz-title">🎮 2025 Game Dev Career Quiz</h1>
            <p class="quiz-subtitle">Discover how your mind naturally works and what type of game development thinking you have! No experience needed - just answer based on what interests you most. Choose 1-2 answers per question for the best results.</p>
        </div>
        
        <!-- Hidden navigation radio buttons -->
        <input type="radio" name="nav" id="nav-q1" class="nav-radio" checked>
        <input type="radio" name="nav" id="nav-q2" class="nav-radio">
        <input type="radio" name="nav" id="nav-q3" class="nav-radio">
        <input type="radio" name="nav" id="nav-q4" class="nav-radio">
        <input type="radio" name="nav" id="nav-q5" class="nav-radio">
        <input type="radio" name="nav" id="nav-q6" class="nav-radio">
        <input type="radio" name="nav" id="nav-q7" class="nav-radio">
        <input type="radio" name="nav" id="nav-q8" class="nav-radio">
        <input type="radio" name="nav" id="nav-q9" class="nav-radio">
        <input type="radio" name="nav" id="nav-q10" class="nav-radio">
        <input type="radio" name="nav" id="nav-q11" class="nav-radio">
        <input type="radio" name="nav" id="nav-q12" class="nav-radio">
        <input type="radio" name="nav" id="nav-results" class="nav-radio">
        
        <!-- Progress bar -->
        <div class="progress-container">
            <div class="progress-bar">
                <div class="progress-fill"></div>
            </div>
        </div>

        <!-- Question 1 -->
        <div class="question-slide q1">
            <div class="question-type">Choose up to 2 options</div>
            <h2 class="question-title">When you imagine starting a creative project, what excites you most?</h2>
            <div class="options-container">
                <div class="option">
                    <input type="checkbox" id="q1_opt1" name="q1" value="creative_3_business_1_leadership_2" data-scores='{"creative_score": 3, "business_score": 1, "leadership_preference": 2}'>
                    <label for="q1_opt1" class="option-label">
                        <span class="option-emoji">🎨</span>
                        <span class="option-text">Imagining the big picture and how everything will look and feel</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q1_opt2" name="q1" value="technical_3_systems_2_detail_2" data-scores='{"technical_score": 3, "systems_score": 2, "detail_orientation": 2}'>
                    <label for="q1_opt2" class="option-label">
                        <span class="option-emoji">⚙️</span>
                        <span class="option-text">Figuring out how things work and solving complex puzzles</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q1_opt3" name="q1" value="visual_3_creative_1_innovation_1" data-scores='{"visual_score": 3, "creative_score": 1, "innovation_drive": 1}'>
                    <label for="q1_opt3" class="option-label">
                        <span class="option-emoji">🎭</span>
                        <span class="option-text">Creating beautiful visuals and artistic concepts</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q1_opt4" name="q1" value="business_2_social_2_user_3" data-scores='{"business_score": 2, "social_score": 2, "systems_score": 1, "user_focus": 3}'>
                    <label for="q1_opt4" class="option-label">
                        <span class="option-emoji">👥</span>
                        <span class="option-text">Understanding what people want and how they'll react</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q1_opt5" name="q1" value="narrative_3_creative_1_user_2" data-scores='{"narrative_score": 3, "creative_score": 1, "user_focus": 2}'>
                    <label for="q1_opt5" class="option-label">
                        <span class="option-emoji">📖</span>
                        <span class="option-text">Crafting stories and emotional experiences</span>
                    </label>
                </div>
            </div>
            <div class="navigation">
                <span class="nav-btn secondary disabled">Previous</span>
                <span class="question-counter"></span>
                <label for="nav-q2" class="nav-btn primary">Next</label>
            </div>
        </div>

        <!-- Question 2 -->
        <div class="question-slide q2">
            <div class="question-type">Choose up to 2 options</div>
            <h2 class="question-title">How do you prefer to receive feedback?</h2>
            <div class="options-container">
                <div class="option">
                    <input type="checkbox" id="q2_opt1" name="q2" value="technical_3_detail_2_collaboration_1" data-scores='{"technical_score": 3, "detail_orientation": 2, "collaboration_style": 1}'>
                    <label for="q2_opt1" class="option-label">
                        <span class="option-emoji">💻</span>
                        <span class="option-text">Technical peer review with specific implementation suggestions</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q2_opt2" name="q2" value="creative_2_visual_2_innovation_2" data-scores='{"creative_score": 2, "visual_score": 2, "audio_score": 1, "innovation_drive": 2}'>
                    <label for="q2_opt2" class="option-label">
                        <span class="option-emoji">🎨</span>
                        <span class="option-text">Creative direction and artistic vision guidance</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q2_opt3" name="q2" value="narrative_2_social_2_user_3" data-scores='{"narrative_score": 2, "social_score": 2, "user_focus": 3}'>
                    <label for="q2_opt3" class="option-label">
                        <span class="option-emoji">❤️</span>
                        <span class="option-text">User reactions and emotional responses to your work</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q2_opt4" name="q2" value="business_3_systems_2_detail_2" data-scores='{"business_score": 3, "systems_score": 2, "detail_orientation": 2}'>
                    <label for="q2_opt4" class="option-label">
                        <span class="option-emoji">📈</span>
                        <span class="option-text">Data-driven insights and performance metrics</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q2_opt5" name="q2" value="creative_2_technical_1_innovation_3" data-scores='{"creative_score": 2, "technical_score": 1, "innovation_drive": 3}'>
                    <label for="q2_opt5" class="option-label">
                        <span class="option-emoji">🚀</span>
                        <span class="option-text">Recognition for innovation and pushing boundaries</span>
                    </label>
                </div>
            </div>
            <div class="navigation">
                <label for="nav-q1" class="nav-btn secondary">Previous</label>
                <span class="question-counter"></span>
                <label for="nav-q3" class="nav-btn primary">Next</label>
            </div>
        </div>

        <!-- Live Preview Section -->
        <div class="live-preview">
            <h3 class="preview-title">🎯 Preview: Based on your selections so far...</h3>
            <p class="preview-text" id="previewText">You're showing strong interest in creative and strategic thinking. This suggests you might think like a <strong>Creative Director</strong> or <strong>Game Designer</strong>!</p>
            <p class="preview-text"><em>Complete all questions for your full personalized results and book recommendation.</em></p>
        </div>
