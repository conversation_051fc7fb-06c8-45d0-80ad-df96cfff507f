<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-Frame-Options" content="ALLOWALL">
    <title>🎮 2025 Game Dev Career Quiz</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            width: 100%;
            margin: 0;
            padding: 20px;
            background: #ffffff;
            box-sizing: border-box;
        }
        
        .quiz-container {
            width: 100%;
            max-width: none;
            padding: 30px;
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            box-sizing: border-box;
        }
        
        .quiz-title {
            font-size: 2.5rem;
            font-weight: bold;
            color: #1a1a1a;
            margin-bottom: 10px;
            text-align: center;
        }
        
        .quiz-subtitle {
            font-size: 1.1rem;
            color: #666;
            line-height: 1.5;
            text-align: center;
            margin-bottom: 30px;
        }
        
        /* Hide navigation radio buttons */
        .nav-radio {
            position: absolute;
            opacity: 0;
            pointer-events: none;
        }
        
        /* Question slides - hidden by default */
        .question-slide {
            display: none;
            animation: fadeIn 0.3s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateX(20px); }
            to { opacity: 1; transform: translateX(0); }
        }
        
        /* Show specific questions when corresponding radio is checked */
        #nav-q1:checked ~ .question-slide.q1 { display: block; }
        #nav-q2:checked ~ .question-slide.q2 { display: block; }
        #nav-q3:checked ~ .question-slide.q3 { display: block; }
        #nav-q4:checked ~ .question-slide.q4 { display: block; }
        #nav-q5:checked ~ .question-slide.q5 { display: block; }
        #nav-q6:checked ~ .question-slide.q6 { display: block; }
        #nav-q7:checked ~ .question-slide.q7 { display: block; }
        #nav-q8:checked ~ .question-slide.q8 { display: block; }
        #nav-q9:checked ~ .question-slide.q9 { display: block; }
        #nav-q10:checked ~ .question-slide.q10 { display: block; }
        #nav-q11:checked ~ .question-slide.q11 { display: block; }
        #nav-q12:checked ~ .question-slide.q12 { display: block; }
        #nav-results:checked ~ .question-slide.results { display: block; }
        
        /* Progress bar */
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #f0f0f0;
            border-radius: 4px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            border-radius: 4px;
            transition: width 0.3s ease;
            width: 8.33%;
        }
        
        /* Update progress based on current question */
        #nav-q1:checked ~ .progress-container .progress-fill { width: 8.33%; }
        #nav-q2:checked ~ .progress-container .progress-fill { width: 16.66%; }
        #nav-q3:checked ~ .progress-container .progress-fill { width: 25%; }
        #nav-q4:checked ~ .progress-container .progress-fill { width: 33.33%; }
        #nav-q5:checked ~ .progress-container .progress-fill { width: 41.66%; }
        #nav-q6:checked ~ .progress-container .progress-fill { width: 50%; }
        #nav-q7:checked ~ .progress-container .progress-fill { width: 58.33%; }
        #nav-q8:checked ~ .progress-container .progress-fill { width: 66.66%; }
        #nav-q9:checked ~ .progress-container .progress-fill { width: 75%; }
        #nav-q10:checked ~ .progress-container .progress-fill { width: 83.33%; }
        #nav-q11:checked ~ .progress-container .progress-fill { width: 91.66%; }
        #nav-q12:checked ~ .progress-container .progress-fill { width: 100%; }
        #nav-results:checked ~ .progress-container .progress-fill { width: 100%; }
        
        .question-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 20px;
            line-height: 1.4;
        }
        
        .question-type {
            font-size: 0.9rem;
            color: #888;
            margin-bottom: 15px;
            font-style: italic;
        }
        
        .options-container {
            display: grid;
            gap: 12px;
            margin-bottom: 30px;
        }
        
        .option {
            position: relative;
        }
        
        .option input[type="checkbox"] {
            position: absolute;
            opacity: 0;
            cursor: pointer;
        }
        
        .option-label {
            display: flex;
            align-items: center;
            padding: 16px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            background: #fafafa;
            gap: 12px;
        }
        
        .option-label:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }
        
        .option input[type="checkbox"]:checked + .option-label {
            border-color: #667eea;
            background: #667eea;
            color: white;
        }
        
        .option-emoji {
            font-size: 1.5rem;
            flex-shrink: 0;
        }
        
        .option-text {
            font-size: 1rem;
            font-weight: 500;
            line-height: 1.4;
        }
        
        .navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
        }
        
        .nav-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .nav-btn.primary {
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .nav-btn.primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        
        .nav-btn.secondary {
            background: #f0f0f0;
            color: #666;
        }
        
        .nav-btn.secondary:hover {
            background: #e0e0e0;
        }
        
        .nav-btn:active {
            transform: translateY(0px) scale(0.98);
        }
        
        .nav-btn:disabled,
        .nav-btn.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            pointer-events: none;
        }
        
        .question-counter {
            font-size: 0.9rem;
            color: #888;
        }
        
        /* Update question counter based on current question */
        #nav-q1:checked ~ .question-slide .question-counter::after { content: "Question 1 of 12"; }
        #nav-q2:checked ~ .question-slide .question-counter::after { content: "Question 2 of 12"; }
        #nav-q3:checked ~ .question-slide .question-counter::after { content: "Question 3 of 12"; }
        #nav-q4:checked ~ .question-slide .question-counter::after { content: "Question 4 of 12"; }
        #nav-q5:checked ~ .question-slide .question-counter::after { content: "Question 5 of 12"; }
        #nav-q6:checked ~ .question-slide .question-counter::after { content: "Question 6 of 12"; }
        #nav-q7:checked ~ .question-slide .question-counter::after { content: "Question 7 of 12"; }
        #nav-q8:checked ~ .question-slide .question-counter::after { content: "Question 8 of 12"; }
        #nav-q9:checked ~ .question-slide .question-counter::after { content: "Question 9 of 12"; }
        #nav-q10:checked ~ .question-slide .question-counter::after { content: "Question 10 of 12"; }
        #nav-q11:checked ~ .question-slide .question-counter::after { content: "Question 11 of 12"; }
        #nav-q12:checked ~ .question-slide .question-counter::after { content: "Question 12 of 12"; }
        #nav-results:checked ~ .question-slide .question-counter::after { content: "Results"; }

        /* Results styling */
        .results-container {
            text-align: center;
            padding: 40px 20px;
        }

        .results-title {
            font-size: 2.5rem;
            font-weight: bold;
            color: #1a1a1a;
            margin-bottom: 20px;
        }

        .results-role {
            font-size: 2rem;
            font-weight: bold;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 20px;
        }

        .results-description {
            font-size: 1.1rem;
            color: #666;
            line-height: 1.6;
            margin-bottom: 30px;
            text-align: left;
        }

        .results-strengths,
        .results-roles,
        .book-recommendation,
        .action-plan {
            text-align: left;
            margin: 30px 0;
            padding: 20px;
            background: #f8f9ff;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .results-strengths h3,
        .results-roles h3,
        .book-recommendation h3,
        .action-plan h3 {
            margin: 0 0 15px 0;
            color: #1a1a1a;
            font-size: 1.2rem;
        }

        .results-strengths ul,
        .action-plan ol {
            margin: 0;
            padding-left: 20px;
            color: #444;
            line-height: 1.6;
        }

        .results-strengths li,
        .action-plan li {
            margin: 8px 0;
        }

        .role-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .role-card {
            padding: 15px;
            background: white;
            border-radius: 8px;
            border: 2px solid #e0e0e0;
            transition: all 0.2s ease;
        }

        .role-card.primary {
            border-color: #667eea;
            background: #667eea;
            color: white;
        }

        .role-card h4 {
            margin: 0 0 8px 0;
            font-size: 1.1rem;
        }

        .role-card p {
            margin: 0;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .book-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-top: 15px;
        }

        .book-card h4 {
            margin: 0 0 10px 0;
            color: #1a1a1a;
            font-size: 1.2rem;
        }

        .book-links {
            display: flex;
            gap: 10px;
            margin: 15px 0;
            flex-wrap: wrap;
        }

        .book-link {
            display: inline-block;
            padding: 8px 16px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            font-size: 0.9rem;
            transition: all 0.2s ease;
        }

        .book-link:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .free-note {
            font-size: 0.9rem;
            color: #2e7d32;
            font-style: italic;
            margin: 10px 0 0 0;
        }

        .encouragement {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #4caf50;
            margin: 30px 0;
            text-align: left;
        }

        .encouragement p {
            margin: 0;
            color: #2e7d32;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="quiz-container">
        <div class="quiz-header">
            <h1 class="quiz-title">🎮 2025 Game Dev Career Quiz</h1>
            <p class="quiz-subtitle">Discover how your mind naturally works and what type of game development thinking you have! No experience needed - just answer based on what interests you most. Choose 1-2 answers per question for the best results.</p>
        </div>
        
        <!-- Hidden navigation radio buttons -->
        <input type="radio" name="nav" id="nav-q1" class="nav-radio" checked>
        <input type="radio" name="nav" id="nav-q2" class="nav-radio">
        <input type="radio" name="nav" id="nav-q3" class="nav-radio">
        <input type="radio" name="nav" id="nav-q4" class="nav-radio">
        <input type="radio" name="nav" id="nav-q5" class="nav-radio">
        <input type="radio" name="nav" id="nav-q6" class="nav-radio">
        <input type="radio" name="nav" id="nav-q7" class="nav-radio">
        <input type="radio" name="nav" id="nav-q8" class="nav-radio">
        <input type="radio" name="nav" id="nav-q9" class="nav-radio">
        <input type="radio" name="nav" id="nav-q10" class="nav-radio">
        <input type="radio" name="nav" id="nav-q11" class="nav-radio">
        <input type="radio" name="nav" id="nav-q12" class="nav-radio">
        <input type="radio" name="nav" id="nav-results" class="nav-radio">
        
        <!-- Progress bar -->
        <div class="progress-container">
            <div class="progress-bar">
                <div class="progress-fill"></div>
            </div>
        </div>

        <!-- Question 1 -->
        <div class="question-slide q1">
            <div class="question-type">Choose up to 2 options</div>
            <h2 class="question-title">When you imagine starting a creative project, what excites you most?</h2>
            <div class="options-container">
                <div class="option">
                    <input type="checkbox" id="q1_opt1" name="q1" value="creative">
                    <label for="q1_opt1" class="option-label">
                        <span class="option-emoji">🎨</span>
                        <span class="option-text">Imagining the big picture and how everything will look and feel</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q1_opt2" name="q1" value="technical">
                    <label for="q1_opt2" class="option-label">
                        <span class="option-emoji">⚙️</span>
                        <span class="option-text">Figuring out how things work and solving complex puzzles</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q1_opt3" name="q1" value="visual">
                    <label for="q1_opt3" class="option-label">
                        <span class="option-emoji">🎭</span>
                        <span class="option-text">Creating beautiful visuals and artistic concepts</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q1_opt4" name="q1" value="user">
                    <label for="q1_opt4" class="option-label">
                        <span class="option-emoji">👥</span>
                        <span class="option-text">Understanding what people want and how they'll react</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q1_opt5" name="q1" value="narrative">
                    <label for="q1_opt5" class="option-label">
                        <span class="option-emoji">📖</span>
                        <span class="option-text">Crafting stories and emotional experiences</span>
                    </label>
                </div>
            </div>
            <div class="navigation">
                <span class="nav-btn secondary disabled">Previous</span>
                <span class="question-counter"></span>
                <label for="nav-q2" class="nav-btn primary">Next</label>
            </div>
        </div>

        <!-- Question 2 -->
        <div class="question-slide q2">
            <div class="question-type">Choose up to 2 options</div>
            <h2 class="question-title">How do you prefer to receive feedback?</h2>
            <div class="options-container">
                <div class="option">
                    <input type="checkbox" id="q2_opt1" name="q2" value="technical">
                    <label for="q2_opt1" class="option-label">
                        <span class="option-emoji">💻</span>
                        <span class="option-text">Technical peer review with specific implementation suggestions</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q2_opt2" name="q2" value="creative">
                    <label for="q2_opt2" class="option-label">
                        <span class="option-emoji">🎨</span>
                        <span class="option-text">Creative direction and artistic vision guidance</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q2_opt3" name="q2" value="user">
                    <label for="q2_opt3" class="option-label">
                        <span class="option-emoji">❤️</span>
                        <span class="option-text">User reactions and emotional responses to your work</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q2_opt4" name="q2" value="business">
                    <label for="q2_opt4" class="option-label">
                        <span class="option-emoji">📈</span>
                        <span class="option-text">Data-driven insights and performance metrics</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q2_opt5" name="q2" value="innovation">
                    <label for="q2_opt5" class="option-label">
                        <span class="option-emoji">🚀</span>
                        <span class="option-text">Recognition for innovation and pushing boundaries</span>
                    </label>
                </div>
            </div>
            <div class="navigation">
                <label for="nav-q1" class="nav-btn secondary">Previous</label>
                <span class="question-counter"></span>
                <label for="nav-q3" class="nav-btn primary">Next</label>
            </div>
        </div>

        <!-- Question 3 -->
        <div class="question-slide q3">
            <div class="question-type">Choose up to 2 options</div>
            <h2 class="question-title">When you encounter a challenging problem, what's your instinct?</h2>
            <div class="options-container">
                <div class="option">
                    <input type="checkbox" id="q3_opt1" name="q3" value="technical">
                    <label for="q3_opt1" class="option-label">
                        <span class="option-emoji">🔬</span>
                        <span class="option-text">Break it down into smaller, logical steps</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q3_opt2" name="q3" value="creative">
                    <label for="q3_opt2" class="option-label">
                        <span class="option-emoji">💡</span>
                        <span class="option-text">Try different creative approaches until something clicks</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q3_opt3" name="q3" value="social">
                    <label for="q3_opt3" class="option-label">
                        <span class="option-emoji">👥</span>
                        <span class="option-text">Talk it through with others to get different perspectives</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q3_opt4" name="q3" value="research">
                    <label for="q3_opt4" class="option-label">
                        <span class="option-emoji">📚</span>
                        <span class="option-text">Research how others have solved similar problems</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q3_opt5" name="q3" value="goal">
                    <label for="q3_opt5" class="option-label">
                        <span class="option-emoji">🎯</span>
                        <span class="option-text">Focus on the end goal and work backwards</span>
                    </label>
                </div>
            </div>
            <div class="navigation">
                <label for="nav-q2" class="nav-btn secondary">Previous</label>
                <span class="question-counter"></span>
                <label for="nav-q4" class="nav-btn primary">Next</label>
            </div>
        </div>

        <!-- Question 4 -->
        <div class="question-slide q4">
            <div class="question-type">Choose up to 2 options</div>
            <h2 class="question-title">How do you naturally like to share your ideas?</h2>
            <div class="options-container">
                <div class="option">
                    <input type="checkbox" id="q4_opt1" name="q4" value="visual">
                    <label for="q4_opt1" class="option-label">
                        <span class="option-emoji">🎨</span>
                        <span class="option-text">Show through visuals, sketches, or prototypes</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q4_opt2" name="q4" value="documentation">
                    <label for="q4_opt2" class="option-label">
                        <span class="option-emoji">📝</span>
                        <span class="option-text">Write detailed explanations and documentation</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q4_opt3" name="q4" value="presentation">
                    <label for="q4_opt3" class="option-label">
                        <span class="option-emoji">🗣️</span>
                        <span class="option-text">Present and discuss in person or meetings</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q4_opt4" name="q4" value="prototype">
                    <label for="q4_opt4" class="option-label">
                        <span class="option-emoji">🎮</span>
                        <span class="option-text">Build something people can actually try</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q4_opt5" name="q4" value="data">
                    <label for="q4_opt5" class="option-label">
                        <span class="option-emoji">📊</span>
                        <span class="option-text">Use data and examples to make the case</span>
                    </label>
                </div>
            </div>
            <div class="navigation">
                <label for="nav-q3" class="nav-btn secondary">Previous</label>
                <span class="question-counter"></span>
                <label for="nav-q5" class="nav-btn primary">Next</label>
            </div>
        </div>

        <!-- Question 5 -->
        <div class="question-slide q5">
            <div class="question-type">Choose up to 2 options</div>
            <h2 class="question-title">What motivates you most in your career?</h2>
            <div class="options-container">
                <div class="option">
                    <input type="checkbox" id="q5_opt1" name="q5" value="technical">
                    <label for="q5_opt1" class="option-label">
                        <span class="option-emoji">🧩</span>
                        <span class="option-text">Solving complex challenges that others can't</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q5_opt2" name="q5" value="creative">
                    <label for="q5_opt2" class="option-label">
                        <span class="option-emoji">🎨</span>
                        <span class="option-text">Bringing creative visions to life</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q5_opt3" name="q5" value="impact">
                    <label for="q5_opt3" class="option-label">
                        <span class="option-emoji">❤️</span>
                        <span class="option-text">Creating experiences that emotionally impact people</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q5_opt4" name="q5" value="scale">
                    <label for="q5_opt4" class="option-label">
                        <span class="option-emoji">📈</span>
                        <span class="option-text">Building products that reach millions</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q5_opt5" name="q5" value="leadership">
                    <label for="q5_opt5" class="option-label">
                        <span class="option-emoji">👑</span>
                        <span class="option-text">Leading teams and shaping company direction</span>
                    </label>
                </div>
            </div>
            <div class="navigation">
                <label for="nav-q4" class="nav-btn secondary">Previous</label>
                <span class="question-counter"></span>
                <label for="nav-q6" class="nav-btn primary">Next</label>
            </div>
        </div>

        <!-- Question 6 -->
        <div class="question-slide q6">
            <div class="question-type">Choose up to 2 options</div>
            <h2 class="question-title">How do you approach learning new skills?</h2>
            <div class="options-container">
                <div class="option">
                    <input type="checkbox" id="q6_opt1" name="q6" value="technical">
                    <label for="q6_opt1" class="option-label">
                        <span class="option-emoji">📚</span>
                        <span class="option-text">Deep study of documentation and technical resources</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q6_opt2" name="q6" value="creative">
                    <label for="q6_opt2" class="option-label">
                        <span class="option-emoji">🎨</span>
                        <span class="option-text">Hands-on experimentation and creative exploration</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q6_opt3" name="q6" value="social">
                    <label for="q6_opt3" class="option-label">
                        <span class="option-emoji">👨‍🏫</span>
                        <span class="option-text">Learning from mentors and peer collaboration</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q6_opt4" name="q6" value="analysis">
                    <label for="q6_opt4" class="option-label">
                        <span class="option-emoji">📊</span>
                        <span class="option-text">Analyzing case studies and industry examples</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q6_opt5" name="q6" value="building">
                    <label for="q6_opt5" class="option-label">
                        <span class="option-emoji">🔨</span>
                        <span class="option-text">Building projects and learning through iteration</span>
                    </label>
                </div>
            </div>
            <div class="navigation">
                <label for="nav-q5" class="nav-btn secondary">Previous</label>
                <span class="question-counter"></span>
                <label for="nav-q7" class="nav-btn primary">Next</label>
            </div>
        </div>

        <!-- Question 7 -->
        <div class="question-slide q7">
            <div class="question-type">Choose up to 2 options</div>
            <h2 class="question-title">What games do you find yourself drawn to?</h2>
            <div class="options-container">
                <div class="option">
                    <input type="checkbox" id="q7_opt1" name="q7" value="technical">
                    <label for="q7_opt1" class="option-label">
                        <span class="option-emoji">🎮</span>
                        <span class="option-text">Technical showcases (realistic graphics, complex systems)</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q7_opt2" name="q7" value="artistic">
                    <label for="q7_opt2" class="option-label">
                        <span class="option-emoji">🎨</span>
                        <span class="option-text">Artistic indies (unique visual style, creative gameplay)</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q7_opt3" name="q7" value="narrative">
                    <label for="q7_opt3" class="option-label">
                        <span class="option-emoji">📖</span>
                        <span class="option-text">Story-rich RPGs (deep narrative, character development)</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q7_opt4" name="q7" value="audio">
                    <label for="q7_opt4" class="option-label">
                        <span class="option-emoji">🎵</span>
                        <span class="option-text">Music/rhythm games (audio-focused, precision-based)</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q7_opt5" name="q7" value="multiplayer">
                    <label for="q7_opt5" class="option-label">
                        <span class="option-emoji">👥</span>
                        <span class="option-text">Multiplayer competitive (community, balance, esports)</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q7_opt6" name="q7" value="casual">
                    <label for="q7_opt6" class="option-label">
                        <span class="option-emoji">📱</span>
                        <span class="option-text">Mobile/casual (accessible, broad appeal)</span>
                    </label>
                </div>
            </div>
            <div class="navigation">
                <label for="nav-q6" class="nav-btn secondary">Previous</label>
                <span class="question-counter"></span>
                <label for="nav-q8" class="nav-btn primary">Next</label>
            </div>
        </div>

        <!-- Question 8 -->
        <div class="question-slide q8">
            <div class="question-type">Choose up to 2 options</div>
            <h2 class="question-title">Choose your workflow preferences</h2>
            <div class="options-container">
                <div class="option">
                    <input type="checkbox" id="q8_opt1" name="q8" value="agile">
                    <label for="q8_opt1" class="option-label">
                        <span class="option-emoji">🏃‍♂️</span>
                        <span class="option-text">Sprint-based with quick iterations</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q8_opt2" name="q8" value="creative">
                    <label for="q8_opt2" class="option-label">
                        <span class="option-emoji">🎨</span>
                        <span class="option-text">Creative exploration with flexible timelines</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q8_opt3" name="q8" value="structured">
                    <label for="q8_opt3" class="option-label">
                        <span class="option-emoji">📋</span>
                        <span class="option-text">Structured phases with clear documentation</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q8_opt4" name="q8" value="feedback">
                    <label for="q8_opt4" class="option-label">
                        <span class="option-emoji">🔄</span>
                        <span class="option-text">Continuous feedback and user testing cycles</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q8_opt5" name="q8" value="prototyping">
                    <label for="q8_opt5" class="option-label">
                        <span class="option-emoji">🚀</span>
                        <span class="option-text">Rapid prototyping and experimentation</span>
                    </label>
                </div>
            </div>
            <div class="navigation">
                <label for="nav-q7" class="nav-btn secondary">Previous</label>
                <span class="question-counter"></span>
                <label for="nav-q9" class="nav-btn primary">Next</label>
            </div>
        </div>

        <!-- Question 9 -->
        <div class="question-slide q9">
            <div class="question-type">Choose up to 2 options</div>
            <h2 class="question-title">What aspect of game development do you find most rewarding?</h2>
            <div class="options-container">
                <div class="option">
                    <input type="checkbox" id="q9_opt1" name="q9" value="technical">
                    <label for="q9_opt1" class="option-label">
                        <span class="option-emoji">⚡</span>
                        <span class="option-text">Solving complex technical challenges that seemed impossible</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q9_opt2" name="q9" value="creative">
                    <label for="q9_opt2" class="option-label">
                        <span class="option-emoji">🎨</span>
                        <span class="option-text">Seeing your creative vision come to life</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q9_opt3" name="q9" value="user">
                    <label for="q9_opt3" class="option-label">
                        <span class="option-emoji">😊</span>
                        <span class="option-text">Watching players enjoy and connect with your work</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q9_opt4" name="q9" value="shipping">
                    <label for="q9_opt4" class="option-label">
                        <span class="option-emoji">🚀</span>
                        <span class="option-text">Shipping a polished product that reaches many people</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q9_opt5" name="q9" value="mentoring">
                    <label for="q9_opt5" class="option-label">
                        <span class="option-emoji">🌱</span>
                        <span class="option-text">Mentoring others and building great teams</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q9_opt6" name="q9" value="innovation">
                    <label for="q9_opt6" class="option-label">
                        <span class="option-emoji">🔬</span>
                        <span class="option-text">Pushing the boundaries of what's technically possible</span>
                    </label>
                </div>
            </div>
            <div class="navigation">
                <label for="nav-q8" class="nav-btn secondary">Previous</label>
                <span class="question-counter"></span>
                <label for="nav-q10" class="nav-btn primary">Next</label>
            </div>
        </div>

        <!-- Question 10 -->
        <div class="question-slide q10">
            <div class="question-type">Rate from 1 (Dislike) to 5 (Love it) - Use checkboxes to indicate interest</div>
            <h2 class="question-title">How much do you enjoy these activities?</h2>
            <div class="options-container">
                <div class="option">
                    <input type="checkbox" id="q10_opt1" name="q10" value="management">
                    <label for="q10_opt1" class="option-label">
                        <span class="option-emoji">👥</span>
                        <span class="option-text">Managing people and their career development</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q10_opt2" name="q10" value="pressure">
                    <label for="q10_opt2" class="option-label">
                        <span class="option-emoji">⏰</span>
                        <span class="option-text">Working under tight deadlines and pressure</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q10_opt3" name="q10" value="presenting">
                    <label for="q10_opt3" class="option-label">
                        <span class="option-emoji">🎨</span>
                        <span class="option-text">Presenting creative work for critique and feedback</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q10_opt4" name="q10" value="debugging">
                    <label for="q10_opt4" class="option-label">
                        <span class="option-emoji">🔧</span>
                        <span class="option-text">Debugging complex technical issues</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q10_opt5" name="q10" value="stakeholders">
                    <label for="q10_opt5" class="option-label">
                        <span class="option-emoji">💬</span>
                        <span class="option-text">Communicating with external stakeholders</span>
                    </label>
                </div>
            </div>
            <div class="navigation">
                <label for="nav-q9" class="nav-btn secondary">Previous</label>
                <span class="question-counter"></span>
                <label for="nav-q11" class="nav-btn primary">Next</label>
            </div>
        </div>

        <!-- Question 11 -->
        <div class="question-slide q11">
            <div class="question-type">Rank your top 2 career motivators - Check your top 2</div>
            <h2 class="question-title">What drives you most in your career?</h2>
            <div class="options-container">
                <div class="option">
                    <input type="checkbox" id="q11_opt1" name="q11" value="challenges">
                    <label for="q11_opt1" class="option-label">
                        <span class="option-emoji">🧩</span>
                        <span class="option-text">Solving complex challenges that others can't</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q11_opt2" name="q11" value="creative">
                    <label for="q11_opt2" class="option-label">
                        <span class="option-emoji">🎨</span>
                        <span class="option-text">Bringing creative visions to life</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q11_opt3" name="q11" value="impact">
                    <label for="q11_opt3" class="option-label">
                        <span class="option-emoji">❤️</span>
                        <span class="option-text">Creating experiences that emotionally impact people</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q11_opt4" name="q11" value="scale">
                    <label for="q11_opt4" class="option-label">
                        <span class="option-emoji">📈</span>
                        <span class="option-text">Building products that reach millions</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q11_opt5" name="q11" value="leadership">
                    <label for="q11_opt5" class="option-label">
                        <span class="option-emoji">👑</span>
                        <span class="option-text">Leading teams and shaping company direction</span>
                    </label>
                </div>
            </div>
            <div class="navigation">
                <label for="nav-q10" class="nav-btn secondary">Previous</label>
                <span class="question-counter"></span>
                <label for="nav-q12" class="nav-btn primary">Next</label>
            </div>
        </div>

        <!-- Question 12 -->
        <div class="question-slide q12">
            <div class="question-type">Final priority check - Check your top 3 most important</div>
            <h2 class="question-title">What matters most to you in your ideal role?</h2>
            <div class="options-container">
                <div class="option">
                    <input type="checkbox" id="q12_opt1" name="q12" value="creative">
                    <label for="q12_opt1" class="option-label">
                        <span class="option-emoji">🎯</span>
                        <span class="option-text">Creative freedom and artistic expression</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q12_opt2" name="q12" value="financial">
                    <label for="q12_opt2" class="option-label">
                        <span class="option-emoji">💰</span>
                        <span class="option-text">Financial success and career growth</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q12_opt3" name="q12" value="technical">
                    <label for="q12_opt3" class="option-label">
                        <span class="option-emoji">🔧</span>
                        <span class="option-text">Technical excellence and innovation</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q12_opt4" name="q12" value="meaningful">
                    <label for="q12_opt4" class="option-label">
                        <span class="option-emoji">❤️</span>
                        <span class="option-text">Meaningful impact on players</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q12_opt5" name="q12" value="collaborative">
                    <label for="q12_opt5" class="option-label">
                        <span class="option-emoji">🤝</span>
                        <span class="option-text">Collaborative team environment</span>
                    </label>
                </div>
                <div class="option">
                    <input type="checkbox" id="q12_opt6" name="q12" value="leadership">
                    <label for="q12_opt6" class="option-label">
                        <span class="option-emoji">👑</span>
                        <span class="option-text">Leadership and decision-making authority</span>
                    </label>
                </div>
            </div>
            <div class="navigation">
                <label for="nav-q11" class="nav-btn secondary">Previous</label>
                <span class="question-counter"></span>
                <label for="nav-results" class="nav-btn primary">See Results</label>
            </div>
        </div>

        <!-- Results Section -->
        <div class="question-slide results">
            <div class="results-container">
                <h2 class="results-title">🎉 Your Game Dev Career Results</h2>

                <div class="results-role">You think like a Creative Visionary!</div>

                <div class="results-description">
                    <p>Based on your responses, you have a natural inclination toward big-picture thinking, creative problem-solving, and bringing innovative concepts to life. You're drawn to the artistic and conceptual side of game development, with a strong focus on user experience and emotional impact.</p>
                </div>

                <div class="results-strengths">
                    <h3>🌟 Your Natural Strengths:</h3>
                    <ul>
                        <li>Visionary thinking and creative problem-solving</li>
                        <li>Strong understanding of user experience and player psychology</li>
                        <li>Ability to communicate ideas through visuals and prototypes</li>
                        <li>Natural inclination toward innovation and pushing boundaries</li>
                        <li>Collaborative approach to bringing ideas to life</li>
                    </ul>
                </div>

                <div class="results-roles">
                    <h3>🎯 Roles That Match Your Thinking Style:</h3>
                    <div class="role-grid">
                        <div class="role-card primary">
                            <h4>🎨 Creative Director</h4>
                            <p>Lead the overall creative vision and artistic direction of games</p>
                        </div>
                        <div class="role-card">
                            <h4>🎮 Game Designer</h4>
                            <p>Design gameplay mechanics, systems, and player experiences</p>
                        </div>
                        <div class="role-card">
                            <h4>🎭 Art Director</h4>
                            <p>Guide the visual style and artistic implementation</p>
                        </div>
                        <div class="role-card">
                            <h4>📖 Narrative Designer</h4>
                            <p>Craft stories and emotional experiences for players</p>
                        </div>
                    </div>
                </div>

                <div class="book-recommendation">
                    <h3>📚 Recommended Reading:</h3>
                    <div class="book-card">
                        <h4>"The Art of Computer Game Design" by Chris Crawford</h4>
                        <p>The foundational text on game design philosophy. Crawford's insights into what makes games engaging and meaningful remain relevant decades later. Essential reading for understanding the art and craft of game design.</p>
                        <div class="book-links">
                            <a href="https://amzn.to/3YJhJJF" target="_blank" class="book-link">📖 Get Paperback</a>
                            <a href="https://amzn.to/3YJhJJF" target="_blank" class="book-link">📱 Get Kindle</a>
                        </div>
                        <p class="free-note">💡 Also available free online at Chris Crawford's website</p>
                    </div>
                </div>

                <div class="action-plan">
                    <h3>🚀 Your Next Steps:</h3>
                    <ol>
                        <li><strong>Start Creating:</strong> Build a portfolio showcasing your creative thinking through game concepts, prototypes, or design documents</li>
                        <li><strong>Learn the Fundamentals:</strong> Study game design principles, player psychology, and user experience design</li>
                        <li><strong>Connect with the Community:</strong> Join game dev communities, attend local meetups, and follow industry leaders</li>
                        <li><strong>Experiment with Tools:</strong> Try game engines like Unity or Unreal, or design tools like Figma and Miro</li>
                        <li><strong>Play Analytically:</strong> Study games you love and break down what makes them work</li>
                    </ol>
                </div>

                <div class="encouragement">
                    <p>🌟 <strong>Remember:</strong> The game industry values diverse perspectives and creative thinking. Your unique combination of interests and natural inclinations is exactly what makes great games possible. Start building, start creating, and don't be afraid to share your ideas with the world!</p>
                </div>

                <div class="navigation">
                    <label for="nav-q1" class="nav-btn primary">Take Quiz Again</label>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div style="background: #f8f9fa; border-top: 2px solid #e9ecef; padding: 20px; margin-top: 40px; text-align: center; font-size: 0.9rem; color: #6c757d;">
            💡 <strong>Supporting This Resource:</strong> This free quiz is supported by affiliate commissions from book recommendations. When you purchase recommended books through our links, you help fund the creation of more career guidance resources. All book recommendations are chosen for their genuine value to game developers.
        </div>
    </div>
</body>
</html>
