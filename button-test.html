<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-Frame-Options" content="ALLOWALL">
    <title>Button Test for Framer</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .option {
            padding: 16px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            background: #fafafa;
            display: flex;
            align-items: center;
            gap: 12px;
            margin: 10px 0;
        }
        
        .option:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }
        
        .option.selected {
            border-color: #667eea;
            background: #667eea;
            color: white;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            margin: 10px;
        }
        
        .btn-primary {
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .status {
            padding: 15px;
            background: #e8f5e8;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #4caf50;
        }
        
        .debug {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 0.9rem;
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Button Test for Framer</h1>
        <p>This tests whether buttons work properly in Framer's HTML embed component.</p>
        
        <h3>Test 1: Option Selection</h3>
        <div class="option" data-option="0">
            <span>🎨</span>
            <span>Click me - Option 1</span>
        </div>
        <div class="option" data-option="1">
            <span>⚙️</span>
            <span>Click me - Option 2</span>
        </div>
        <div class="option" data-option="2">
            <span>🎭</span>
            <span>Click me - Option 3</span>
        </div>
        
        <h3>Test 2: Navigation Buttons</h3>
        <button class="btn btn-primary" id="testBtn">Test Button</button>
        <button class="btn btn-primary" id="resetBtn">Reset Test</button>
        
        <div class="status" id="status">
            Status: Ready for testing
        </div>
        
        <h3>Debug Log</h3>
        <div class="debug" id="debugLog">
            Initializing...
        </div>
    </div>

    <script>
        let selectedOptions = [];
        let clickCount = 0;
        
        function log(message) {
            const debugLog = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            debugLog.innerHTML += `<br>[${timestamp}] ${message}`;
            debugLog.scrollTop = debugLog.scrollHeight;
            console.log(message);
        }
        
        function updateStatus(message) {
            document.getElementById('status').textContent = `Status: ${message}`;
            log(`Status updated: ${message}`);
        }
        
        function selectOption(optionIndex) {
            log(`selectOption called with index: ${optionIndex}`);
            
            const option = document.querySelector(`[data-option="${optionIndex}"]`);
            if (option) {
                // Toggle selection
                if (option.classList.contains('selected')) {
                    option.classList.remove('selected');
                    selectedOptions = selectedOptions.filter(i => i !== optionIndex);
                    log(`Option ${optionIndex} deselected`);
                } else {
                    option.classList.add('selected');
                    selectedOptions.push(optionIndex);
                    log(`Option ${optionIndex} selected`);
                }
                
                updateStatus(`Selected options: [${selectedOptions.join(', ')}]`);
            }
        }
        
        function testButtonClick() {
            clickCount++;
            log(`Test button clicked ${clickCount} times`);
            updateStatus(`Test button clicked ${clickCount} times`);
        }
        
        function resetTest() {
            log('Reset button clicked');
            selectedOptions = [];
            clickCount = 0;
            
            // Clear all selections
            document.querySelectorAll('.option').forEach(option => {
                option.classList.remove('selected');
            });
            
            updateStatus('Test reset');
            log('Test reset complete');
        }
        
        // Initialize event listeners
        function initializeEventListeners() {
            log('Initializing event listeners...');
            
            // Method 1: Event delegation for options
            document.addEventListener('click', function(e) {
                log(`Click detected on: ${e.target.tagName} ${e.target.className}`);
                
                if (e.target.closest('.option')) {
                    const option = e.target.closest('.option');
                    const optionIndex = parseInt(option.getAttribute('data-option'));
                    log(`Option element clicked, index: ${optionIndex}`);
                    if (!isNaN(optionIndex)) {
                        selectOption(optionIndex);
                    }
                }
            });
            
            // Method 2: Direct event listeners for buttons
            const testBtn = document.getElementById('testBtn');
            const resetBtn = document.getElementById('resetBtn');
            
            if (testBtn) {
                testBtn.addEventListener('click', function(e) {
                    log('Test button event listener triggered');
                    testButtonClick();
                });
                log('Test button listener attached');
            }
            
            if (resetBtn) {
                resetBtn.addEventListener('click', function(e) {
                    log('Reset button event listener triggered');
                    resetTest();
                });
                log('Reset button listener attached');
            }
            
            log('Event listeners initialization complete');
        }
        
        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeEventListeners);
        } else {
            initializeEventListeners();
        }
        
        log('Script loaded successfully');
        updateStatus('Ready for testing');
    </script>
</body>
</html>
