<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-Frame-Options" content="ALLOWALL">
    <title>Form-Based Test for Framer</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            width: 100%;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            box-sizing: border-box;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            width: 100%;
            max-width: none;
            box-sizing: border-box;
        }
        
        .form-option {
            display: flex;
            align-items: center;
            padding: 16px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.2s ease;
            background: #fafafa;
        }
        
        .form-option:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }
        
        .form-option input[type="checkbox"] {
            margin-right: 12px;
            transform: scale(1.2);
        }
        
        .form-option input[type="checkbox"]:checked + .option-content {
            color: #667eea;
            font-weight: 600;
        }
        
        .option-content {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            margin: 10px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        
        .status {
            padding: 15px;
            background: #e8f5e8;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #4caf50;
        }
        
        .debug {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 0.9rem;
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
        }
        
        /* Hide default radio/checkbox styling and create custom */
        .custom-radio {
            position: relative;
            display: flex;
            align-items: center;
            padding: 16px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.2s ease;
            background: #fafafa;
        }
        
        .custom-radio input[type="radio"] {
            position: absolute;
            opacity: 0;
            cursor: pointer;
        }
        
        .custom-radio:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }
        
        .custom-radio input[type="radio"]:checked ~ .radio-content {
            color: #667eea;
            font-weight: 600;
        }
        
        .custom-radio input[type="radio"]:checked {
            background: #667eea;
        }
        
        .radio-content {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        /* CSS-only status feedback */
        .status-display {
            counter-reset: checkbox-count radio-count;
        }

        .form-option input[type="checkbox"]:checked {
            counter-increment: checkbox-count;
        }

        .custom-radio input[type="radio"]:checked {
            counter-increment: radio-count;
        }

        .checkbox-counter::after {
            content: counter(checkbox-count);
            background: #667eea;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            margin-left: 8px;
            font-weight: bold;
        }

        .radio-counter::after {
            content: counter(radio-count);
            background: #ff6b6b;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            margin-left: 8px;
            font-weight: bold;
        }

        /* Visual feedback for button interactions */
        .btn:active {
            transform: translateY(0px) scale(0.98);
            box-shadow: 0 2px 6px rgba(102, 126, 234, 0.3);
        }

        /* Show different content based on selections */
        .feedback-section {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            background: #f0f0f0;
            display: none;
        }

        /* Show feedback when checkboxes are selected */
        .form-option input[type="checkbox"]:checked ~ .feedback-section,
        input[type="checkbox"]:checked ~ .feedback-section {
            display: block;
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
        }

        /* Show feedback when radio is selected */
        .custom-radio input[type="radio"]:checked ~ .feedback-section {
            display: block;
            background: #fff3e0;
            border-left: 4px solid #ff9800;
        }
    </style>
</head>
<body>
    <div class="container status-display">
        <h1>📝 Form-Based Test for Framer</h1>
        <p>This uses form elements which often work better in iframe environments.</p>

        <form id="testForm">
            <h3>Test 1: Checkbox Options <span class="checkbox-counter">Selected: </span></h3>
            <label class="form-option">
                <input type="checkbox" name="options" value="0">
                <div class="option-content">
                    <span>🎨</span>
                    <span>Checkbox Option 1</span>
                </div>
            </label>
            
            <label class="form-option">
                <input type="checkbox" name="options" value="1">
                <div class="option-content">
                    <span>⚙️</span>
                    <span>Checkbox Option 2</span>
                </div>
            </label>
            
            <label class="form-option">
                <input type="checkbox" name="options" value="2">
                <div class="option-content">
                    <span>🎭</span>
                    <span>Checkbox Option 3</span>
                </div>
            </label>
            
            <h3>Test 2: Radio Options <span class="radio-counter">Selected: </span></h3>
            <label class="custom-radio">
                <input type="radio" name="radio-test" value="a">
                <div class="radio-content">
                    <span>🔥</span>
                    <span>Radio Option A</span>
                </div>
            </label>
            
            <label class="custom-radio">
                <input type="radio" name="radio-test" value="b">
                <div class="radio-content">
                    <span>⭐</span>
                    <span>Radio Option B</span>
                </div>
            </label>
            
            <label class="custom-radio">
                <input type="radio" name="radio-test" value="c">
                <div class="radio-content">
                    <span>🚀</span>
                    <span>Radio Option C</span>
                </div>
            </label>
            
            <div class="feedback-section">
                <p>✅ Great! You've made some checkbox selections. The CSS counters above show how many you've selected.</p>
            </div>

            <div class="feedback-section">
                <p>🔘 Excellent! You've selected a radio option. Notice how the counter updates automatically.</p>
            </div>

            <h3>Test 3: Form Buttons</h3>
            <button type="button" class="btn" id="testBtn">Test Button (Click & Hold)</button>
            <button type="reset" class="btn" id="resetBtn">Reset Form</button>
        </form>
        
        <div class="status" id="status">
            Status: Ready for testing
        </div>
        
        <h3>Debug Log</h3>
        <div class="debug" id="debugLog">
            Initializing form-based test...
        </div>
    </div>

    <script>
        function log(message) {
            const debugLog = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            debugLog.innerHTML += `<br>[${timestamp}] ${message}`;
            debugLog.scrollTop = debugLog.scrollHeight;
            console.log(message);
        }
        
        function updateStatus(message) {
            document.getElementById('status').textContent = `Status: ${message}`;
            log(`Status: ${message}`);
        }
        
        function getFormData() {
            const form = document.getElementById('testForm');
            const formData = new FormData(form);
            
            const checkboxes = [];
            const radios = [];
            
            for (let [key, value] of formData.entries()) {
                if (key === 'options') {
                    checkboxes.push(value);
                } else if (key === 'radio-test') {
                    radios.push(value);
                }
            }
            
            return { checkboxes, radios };
        }
        
        function handleFormChange() {
            const data = getFormData();
            log(`Form changed - Checkboxes: [${data.checkboxes.join(', ')}], Radio: ${data.radios[0] || 'none'}`);
            updateStatus(`Selected - Checkboxes: [${data.checkboxes.join(', ')}], Radio: ${data.radios[0] || 'none'}`);
        }
        
        function handleTestButton() {
            log('Test button clicked via form');
            const data = getFormData();
            updateStatus(`Test button clicked! Current selections - Checkboxes: [${data.checkboxes.join(', ')}], Radio: ${data.radios[0] || 'none'}`);
        }
        
        function handleReset() {
            log('Form reset');
            setTimeout(() => {
                updateStatus('Form reset complete');
            }, 100);
        }
        
        // Initialize form event listeners
        function initializeFormListeners() {
            log('Initializing form listeners...');
            
            const form = document.getElementById('testForm');
            const testBtn = document.getElementById('testBtn');
            
            // Listen for form changes
            form.addEventListener('change', handleFormChange);
            form.addEventListener('input', handleFormChange);
            
            // Listen for button clicks
            testBtn.addEventListener('click', handleTestButton);
            
            // Listen for form reset
            form.addEventListener('reset', handleReset);
            
            log('Form listeners initialized');
        }
        
        // Initialize
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeFormListeners);
        } else {
            initializeFormListeners();
        }
        
        log('Form-based test loaded successfully');
        updateStatus('Ready - Try checking boxes and clicking buttons');
    </script>
</body>
</html>
